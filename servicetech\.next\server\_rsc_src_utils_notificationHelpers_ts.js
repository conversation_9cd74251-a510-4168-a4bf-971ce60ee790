"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_notificationHelpers_ts";
exports.ids = ["_rsc_src_utils_notificationHelpers_ts"];
exports.modules = {

/***/ "(rsc)/./src/services/NotificationService.ts":
/*!*********************************************!*\
  !*** ./src/services/NotificationService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n/* harmony import */ var _templates_email_EmailTemplates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/templates/email/EmailTemplates */ \"(rsc)/./src/templates/email/EmailTemplates.ts\");\n/* harmony import */ var _SMSService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SMSService */ \"(rsc)/./src/services/SMSService.ts\");\n/* harmony import */ var _PushService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PushService */ \"(rsc)/./src/services/PushService.ts\");\n\n\n\n\n\nclass NotificationService {\n    constructor(){\n        this.supabase = (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createAdminClient)();\n        const apiKey = process.env.RESEND_API_KEY;\n        this.fromEmail = process.env.RESEND_FROM_EMAIL ?? '<EMAIL>';\n        if (!apiKey) {\n            throw new Error('RESEND_API_KEY não configurada');\n        }\n        this.resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(apiKey);\n        // Inicializar serviços SMS e Push se as credenciais estiverem disponíveis\n        try {\n            this.smsService = new _SMSService__WEBPACK_IMPORTED_MODULE_3__.SMSService();\n        } catch (error) {\n            console.warn('⚠️ SMS Service não inicializado:', error);\n        }\n        try {\n            this.pushService = new _PushService__WEBPACK_IMPORTED_MODULE_4__.PushService();\n        } catch (error) {\n            console.warn('⚠️ Push Service não inicializado:', error);\n        }\n    }\n    /**\r\n   * Processa uma notificação completa (cria registro + envia)\r\n   */ async processarNotificacao(data) {\n        try {\n            console.log(`📧 Processando notificação: ${data.tipo} para usuário ${data.destinatario_id} via ${data.canal ?? 'email'}`);\n            // 1. Obter dados do destinatário e suas preferências\n            const { data: { user }, error: userError } = await this.supabase.auth.admin.getUserById(data.destinatario_id);\n            if (userError || !user) {\n                throw new Error(`Usuário não encontrado: ${data.destinatario_id}`);\n            }\n            // 2. Obter preferências de notificação do usuário\n            const preferencias = await this.obterPreferenciasUsuario(data.destinatario_id);\n            // 3. Determinar canais a serem usados\n            const canais = this.determinarCanais(data.canal, preferencias, data.tipo);\n            if (canais.length === 0) {\n                console.log(`⚠️ Nenhum canal habilitado para usuário ${data.destinatario_id}`);\n                return {\n                    success: true\n                }; // Não é erro, usuário optou por não receber\n            }\n            // 4. Processar cada canal\n            const resultados = [];\n            for (const canal of canais){\n                const resultado = await this.processarCanalNotificacao(canal, data, user, preferencias);\n                resultados.push(resultado);\n            }\n            // 5. Consolidar resultados\n            const sucessos = resultados.filter((r)=>r.success).length;\n            const falhas = resultados.filter((r)=>!r.success);\n            return {\n                success: sucessos > 0,\n                notificacao_id: resultados.find((r)=>r.notificacao_id)?.notificacao_id,\n                error: falhas.length > 0 ? falhas.map((f)=>f.error).join('; ') : undefined\n            };\n        } catch (error) {\n            console.error('❌ Erro ao processar notificação:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro desconhecido'\n            };\n        }\n    }\n    /**\r\n   * Obter preferências de notificação do usuário\r\n   */ async obterPreferenciasUsuario(userId) {\n        const { data } = await this.supabase.from('preferencias_notificacao').select('*').eq('user_id', userId).single();\n        // Retorna preferências padrão se não encontrar\n        return data ?? {\n            email_enabled: true,\n            sms_enabled: true,\n            push_enabled: true,\n            tipos_habilitados: [\n                'novo_agendamento',\n                'agendamento_confirmado',\n                'agendamento_recusado',\n                'agendamento_cancelado',\n                'lembrete_confirmacao',\n                'lembrete_agendamento',\n                'pagamento_confirmado'\n            ]\n        };\n    }\n    /**\r\n   * Determinar quais canais usar baseado nas preferências\r\n   */ determinarCanais(canalSolicitado, preferencias, tipo) {\n        const canais = [];\n        // Se canal específico foi solicitado, usar apenas ele (se habilitado)\n        if (canalSolicitado) {\n            if (this.canalHabilitado(canalSolicitado, preferencias, tipo)) {\n                canais.push(canalSolicitado);\n            }\n            return canais;\n        }\n        // Caso contrário, usar todos os canais habilitados\n        if (this.canalHabilitado('email', preferencias, tipo)) canais.push('email');\n        if (this.canalHabilitado('sms', preferencias, tipo)) canais.push('sms');\n        if (this.canalHabilitado('push', preferencias, tipo)) canais.push('push');\n        return canais;\n    }\n    /**\r\n   * Verificar se um canal está habilitado para o usuário\r\n   */ canalHabilitado(canal, preferencias, tipo) {\n        if (!preferencias) return canal === 'email'; // Default apenas email\n        // Verificar se o tipo de notificação está habilitado\n        if (tipo && preferencias.tipos_habilitados && !preferencias.tipos_habilitados.includes(tipo)) {\n            return false;\n        }\n        // Verificar se o canal está habilitado\n        switch(canal){\n            case 'email':\n                return preferencias.email_enabled !== false;\n            case 'sms':\n                return preferencias.sms_enabled === true && !!this.smsService;\n            case 'push':\n                return preferencias.push_enabled === true && !!this.pushService;\n            default:\n                return false;\n        }\n    }\n    /**\r\n   * Processar notificação para um canal específico\r\n   */ async processarCanalNotificacao(canal, data, user, preferencias) {\n        try {\n            // Criar registro de notificação\n            const notificacao = await this.criarRegistroNotificacao(data, canal);\n            let resultado;\n            // Enviar via canal específico\n            switch(canal){\n                case 'email':\n                    resultado = await this.enviarViaEmail(data, user);\n                    break;\n                case 'sms':\n                    resultado = await this.enviarViaSMS(data, user);\n                    break;\n                case 'push':\n                    resultado = await this.enviarViaPush(data, user);\n                    break;\n                default:\n                    throw new Error(`Canal não suportado: ${canal}`);\n            }\n            // Atualizar status da notificação\n            await this.atualizarStatusNotificacao(notificacao.notificacao_id, resultado.success, resultado.error);\n            return {\n                success: resultado.success,\n                notificacao_id: notificacao.notificacao_id,\n                error: resultado.error\n            };\n        } catch (error) {\n            console.error(`❌ Erro ao processar canal ${canal}:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro desconhecido'\n            };\n        }\n    }\n    /**\r\n   * Envia email usando Resend\r\n   */ async enviarEmail(data) {\n        try {\n            const result = await this.resend.emails.send({\n                from: data.from ?? this.fromEmail,\n                to: data.to,\n                subject: data.subject,\n                html: data.html,\n                text: data.text\n            });\n            console.log(`✅ Email enviado com sucesso: ${result.data?.id}`);\n            return {\n                success: true,\n                messageId: result.data?.id\n            };\n        } catch (error) {\n            console.error('❌ Erro ao enviar email:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro ao enviar email'\n            };\n        }\n    }\n    /**\r\n   * Criar registro de notificação no banco\r\n   */ async criarRegistroNotificacao(data, canal) {\n        const template = await this.gerarTemplate(data.tipo, data.contexto);\n        const notificacaoData = {\n            user_id: data.destinatario_id,\n            tipo_notificacao: data.tipo,\n            titulo: template.subject,\n            mensagem: template.text ?? 'Notificação',\n            dados_contexto: data.contexto,\n            canal: canal,\n            agendamento_id: data.agendamento_id,\n            empresa_id: data.empresa_id\n        };\n        const { data: notificacao, error } = await this.supabase.from('notificacoes').insert([\n            notificacaoData\n        ]).select().single();\n        if (error) {\n            throw new Error(`Erro ao criar notificação: ${error.message}`);\n        }\n        return notificacao;\n    }\n    /**\r\n   * Enviar via email\r\n   */ async enviarViaEmail(data, user) {\n        if (!user.email) {\n            throw new Error('Usuário não possui email');\n        }\n        const template = await this.gerarTemplate(data.tipo, data.contexto);\n        return this.enviarEmail({\n            to: user.email,\n            subject: template.subject,\n            html: template.html,\n            text: template.text\n        });\n    }\n    /**\r\n   * Enviar via SMS\r\n   */ async enviarViaSMS(data, user) {\n        if (!this.smsService) {\n            throw new Error('Serviço de SMS não disponível');\n        }\n        // Obter telefone do usuário (pode estar em user_metadata ou perfil)\n        const telefone = user.user_metadata?.telefone ?? user.phone;\n        if (!telefone) {\n            throw new Error('Usuário não possui telefone');\n        }\n        if (!this.smsService.validarNumero(telefone)) {\n            throw new Error('Número de telefone inválido');\n        }\n        const mensagem = this.smsService.gerarTemplateSMS(data.tipo, data.contexto);\n        return this.smsService.enviarSMS({\n            to: telefone,\n            message: mensagem\n        });\n    }\n    /**\r\n   * Enviar via Push\r\n   */ async enviarViaPush(data, user) {\n        if (!this.pushService) {\n            throw new Error('Serviço de Push não disponível');\n        }\n        // Obter tokens de dispositivo do usuário\n        const { data: tokens } = await this.supabase.from('device_tokens').select('token').eq('user_id', user.id).eq('active', true);\n        if (!tokens || tokens.length === 0) {\n            throw new Error('Usuário não possui tokens de dispositivo ativos');\n        }\n        const dadosPush = this.pushService.gerarDadosPush(data.tipo, data.contexto);\n        // Enviar para todos os tokens do usuário\n        const resultados = await Promise.all(tokens.map(({ token })=>this.pushService.enviarPush({\n                ...dadosPush,\n                token\n            })));\n        // Considerar sucesso se pelo menos um envio foi bem-sucedido\n        const sucessos = resultados.filter((r)=>r.success);\n        const falhas = resultados.filter((r)=>!r.success);\n        return {\n            success: sucessos.length > 0,\n            messageId: sucessos[0]?.messageId,\n            error: falhas.length > 0 ? falhas.map((f)=>f.error).join('; ') : undefined\n        };\n    }\n    /**\r\n   * Gera template de email baseado no tipo de notificação\r\n   */ async gerarTemplate(tipo, contexto) {\n        const templates = new _templates_email_EmailTemplates__WEBPACK_IMPORTED_MODULE_2__.EmailTemplates();\n        switch(tipo){\n            case 'novo_agendamento':\n                return templates.novoAgendamentoCliente(contexto);\n            case 'agendamento_confirmado':\n                return templates.agendamentoConfirmado(contexto);\n            case 'agendamento_recusado':\n                return templates.agendamentoRecusado(contexto);\n            case 'agendamento_cancelado':\n                return templates.agendamentoCancelado(contexto);\n            case 'lembrete_confirmacao':\n                return templates.lembreteConfirmacao(contexto);\n            case 'lembrete_agendamento':\n                return templates.lembreteAgendamento(contexto);\n            case 'pagamento_confirmado':\n                return templates.pagamentoConfirmado(contexto);\n            default:\n                throw new Error(`Tipo de notificação não suportado: ${tipo}`);\n        }\n    }\n    /**\r\n   * Atualiza status da notificação após tentativa de envio\r\n   */ async atualizarStatusNotificacao(notificacaoId, sucesso, erro) {\n        try {\n            const updateData = {\n                enviada: sucesso,\n                data_envio: new Date().toISOString(),\n                tentativas_envio: 1\n            };\n            if (!sucesso && erro) {\n                updateData.erro_envio = erro;\n            }\n            await this.supabase.from('notificacoes').update(updateData).eq('notificacao_id', notificacaoId);\n        } catch (error) {\n            console.error('❌ Erro ao atualizar status da notificação:', error);\n        }\n    }\n    /**\r\n   * Reenviar notificação que falhou\r\n   */ async reenviarNotificacao(notificacaoId) {\n        try {\n            // Buscar notificação\n            const { data: notificacao, error } = await this.supabase.from('notificacoes').select('*').eq('notificacao_id', notificacaoId).single();\n            if (error || !notificacao) {\n                throw new Error('Notificação não encontrada');\n            }\n            // Obter dados do usuário\n            const { data: { user }, error: userError } = await this.supabase.auth.admin.getUserById(notificacao.user_id);\n            if (userError || !user?.email) {\n                throw new Error('Usuário não encontrado ou sem email');\n            }\n            // Gerar template novamente\n            const template = await this.gerarTemplate(notificacao.tipo_notificacao, notificacao.dados_contexto);\n            // Enviar email\n            const emailResult = await this.enviarEmail({\n                to: user.email,\n                subject: template.subject,\n                html: template.html,\n                text: template.text\n            });\n            // Atualizar tentativas\n            await this.supabase.from('notificacoes').update({\n                enviada: emailResult.success,\n                data_envio: new Date().toISOString(),\n                tentativas_envio: notificacao.tentativas_envio + 1,\n                erro_envio: emailResult.success ? null : emailResult.error\n            }).eq('notificacao_id', notificacaoId);\n            return {\n                success: emailResult.success,\n                notificacao_id: notificacaoId,\n                error: emailResult.error,\n                tentativas: notificacao.tentativas_envio + 1\n            };\n        } catch (error) {\n            console.error('❌ Erro ao reenviar notificação:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro desconhecido'\n            };\n        }\n    }\n    /**\r\n   * Buscar notificações pendentes para reenvio\r\n   */ async buscarNotificacoesPendentes(limite = 10) {\n        try {\n            const { data, error } = await this.supabase.from('notificacoes').select('*').eq('enviada', false).lt('tentativas_envio', 3).order('created_at', {\n                ascending: true\n            }).limit(limite);\n            if (error) {\n                throw new Error(`Erro ao buscar notificações pendentes: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error('❌ Erro ao buscar notificações pendentes:', error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/NotificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/PushService.ts":
/*!*************************************!*\
  !*** ./src/services/PushService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PushService: () => (/* binding */ PushService)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n\nclass PushService {\n    constructor(){\n        // Inicializar Firebase Admin apenas se não estiver inicializado\n        if (!firebase_admin__WEBPACK_IMPORTED_MODULE_0__.apps.length) {\n            const projectId = process.env.FIREBASE_PROJECT_ID;\n            const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\n            const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n            if (!projectId || !privateKey || !clientEmail) {\n                throw new Error('Credenciais do Firebase não configuradas');\n            }\n            this.app = firebase_admin__WEBPACK_IMPORTED_MODULE_0__.initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0__.credential.cert({\n                    projectId,\n                    privateKey,\n                    clientEmail\n                }),\n                projectId\n            });\n        } else {\n            this.app = firebase_admin__WEBPACK_IMPORTED_MODULE_0__.apps[0];\n        }\n    }\n    /**\r\n   * Envia notificação push usando Firebase Cloud Messaging\r\n   */ async enviarPush(data) {\n        try {\n            console.log(`🔔 Enviando push notification para token: ${data.token.substring(0, 20)}...`);\n            const message = {\n                token: data.token,\n                notification: {\n                    title: data.title,\n                    body: data.body,\n                    imageUrl: data.imageUrl\n                },\n                data: data.data || {},\n                webpush: {\n                    notification: {\n                        title: data.title,\n                        body: data.body,\n                        icon: '/icon-192x192.png',\n                        badge: '/badge-72x72.png',\n                        image: data.imageUrl,\n                        requireInteraction: true,\n                        actions: [\n                            {\n                                action: 'view',\n                                title: 'Ver Detalhes'\n                            },\n                            {\n                                action: 'dismiss',\n                                title: 'Dispensar'\n                            }\n                        ]\n                    },\n                    fcmOptions: {\n                        link: data.data?.url || '/'\n                    }\n                }\n            };\n            const response = await firebase_admin__WEBPACK_IMPORTED_MODULE_0__.messaging().send(message);\n            console.log(`✅ Push notification enviada com sucesso: ${response}`);\n            return {\n                success: true,\n                messageId: response\n            };\n        } catch (error) {\n            console.error('❌ Erro ao enviar push notification:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro ao enviar push notification'\n            };\n        }\n    }\n    /**\r\n   * Envia notificação push para múltiplos tokens\r\n   */ async enviarPushMultiplo(tokens, data) {\n        const promises = tokens.map((token)=>this.enviarPush({\n                ...data,\n                token\n            }));\n        return Promise.all(promises);\n    }\n    /**\r\n   * Gerar dados de push baseado no tipo de notificação\r\n   */ gerarDadosPush(tipo, contexto) {\n        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n        switch(tipo){\n            case 'novo_agendamento':\n                return {\n                    title: '📅 Novo Agendamento',\n                    body: `Agendamento de ${contexto.servico_nome} solicitado para ${this.formatarDataPush(contexto.data_hora_inicio)}`,\n                    data: {\n                        type: 'agendamento',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/cliente/agendamentos`\n                    }\n                };\n            case 'agendamento_confirmado':\n                return {\n                    title: '✅ Agendamento Confirmado',\n                    body: `${contexto.servico_nome} confirmado para ${this.formatarDataPush(contexto.data_hora_inicio)}`,\n                    data: {\n                        type: 'confirmacao',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/cliente/agendamentos`\n                    }\n                };\n            case 'agendamento_recusado':\n                return {\n                    title: '❌ Agendamento Recusado',\n                    body: `Seu agendamento de ${contexto.servico_nome} foi recusado`,\n                    data: {\n                        type: 'recusa',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/buscar`\n                    }\n                };\n            case 'agendamento_cancelado':\n                return {\n                    title: '🚫 Agendamento Cancelado',\n                    body: `Agendamento de ${contexto.servico_nome} foi cancelado`,\n                    data: {\n                        type: 'cancelamento',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/cliente/agendamentos`\n                    }\n                };\n            case 'lembrete_confirmacao':\n                return {\n                    title: '⏰ Confirmação Pendente',\n                    body: `Agendamento de ${contexto.cliente_nome} expira em breve`,\n                    data: {\n                        type: 'lembrete_confirmacao',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/proprietario/agendamentos`\n                    }\n                };\n            case 'lembrete_agendamento':\n                return {\n                    title: '🔔 Lembrete de Agendamento',\n                    body: `${contexto.servico_nome} amanhã às ${this.formatarHoraPush(contexto.data_hora_inicio)}`,\n                    data: {\n                        type: 'lembrete_agendamento',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/cliente/agendamentos`\n                    }\n                };\n            case 'pagamento_confirmado':\n                return {\n                    title: '💳 Pagamento Confirmado',\n                    body: `Pagamento de ${contexto.servico_nome} processado com sucesso`,\n                    data: {\n                        type: 'pagamento',\n                        agendamento_id: contexto.agendamento_id?.toString(),\n                        url: `${baseUrl}/cliente/agendamentos`\n                    }\n                };\n            default:\n                return {\n                    title: '🔔 ServiceTech',\n                    body: 'Você tem uma nova notificação',\n                    data: {\n                        type: 'geral',\n                        url: baseUrl\n                    }\n                };\n        }\n    }\n    /**\r\n   * Formatar data para push notification\r\n   */ formatarDataPush(dataISO) {\n        const data = new Date(dataISO);\n        return data.toLocaleDateString('pt-BR', {\n            day: '2-digit',\n            month: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }\n    /**\r\n   * Formatar hora para push notification\r\n   */ formatarHoraPush(dataISO) {\n        const data = new Date(dataISO);\n        return data.toLocaleTimeString('pt-BR', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }\n    /**\r\n   * Validar token de dispositivo\r\n   */ validarToken(token) {\n        // Token FCM tem formato específico e comprimento mínimo\n        return !!(token && token.length > 100 && /^[A-Za-z0-9_-]+$/.test(token));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/PushService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/SMSService.ts":
/*!************************************!*\
  !*** ./src/services/SMSService.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SMSService: () => (/* binding */ SMSService)\n/* harmony export */ });\n/* harmony import */ var twilio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! twilio */ \"(rsc)/./node_modules/twilio/lib/index.js\");\n/* harmony import */ var twilio__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(twilio__WEBPACK_IMPORTED_MODULE_0__);\n\nclass SMSService {\n    constructor(){\n        const accountSid = process.env.TWILIO_ACCOUNT_SID;\n        const authToken = process.env.TWILIO_AUTH_TOKEN;\n        this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '';\n        if (!accountSid || !authToken) {\n            throw new Error('Credenciais do Twilio não configuradas');\n        }\n        if (!this.fromNumber) {\n            throw new Error('Número do Twilio não configurado');\n        }\n        this.client = new twilio__WEBPACK_IMPORTED_MODULE_0__.Twilio(accountSid, authToken);\n    }\n    /**\r\n   * Envia SMS usando Twilio\r\n   */ async enviarSMS(data) {\n        try {\n            console.log(`📱 Enviando SMS para: ${data.to}`);\n            // Validar formato do número (deve incluir código do país)\n            const numeroFormatado = this.formatarNumero(data.to);\n            const message = await this.client.messages.create({\n                body: data.message,\n                from: data.from || this.fromNumber,\n                to: numeroFormatado\n            });\n            console.log(`✅ SMS enviado com sucesso: ${message.sid}`);\n            return {\n                success: true,\n                messageId: message.sid\n            };\n        } catch (error) {\n            console.error('❌ Erro ao enviar SMS:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Erro ao enviar SMS'\n            };\n        }\n    }\n    /**\r\n   * Formatar número de telefone para padrão internacional\r\n   */ formatarNumero(numero) {\n        // Remove caracteres não numéricos\n        const apenasNumeros = numero.replace(/\\D/g, '');\n        // Se já tem código do país (+55), retorna como está\n        if (apenasNumeros.startsWith('55') && apenasNumeros.length === 13) {\n            return `+${apenasNumeros}`;\n        }\n        // Se é número brasileiro sem código do país, adiciona +55\n        if (apenasNumeros.length === 11) {\n            return `+55${apenasNumeros}`;\n        }\n        // Se é número brasileiro com DDD sem 9, adiciona 9\n        if (apenasNumeros.length === 10) {\n            const ddd = apenasNumeros.substring(0, 2);\n            const numero = apenasNumeros.substring(2);\n            return `+55${ddd}9${numero}`;\n        }\n        // Retorna como está se não conseguir formatar\n        return numero;\n    }\n    /**\r\n   * Validar se o número é válido para SMS\r\n   */ validarNumero(numero) {\n        const apenasNumeros = numero.replace(/\\D/g, '');\n        // Número brasileiro deve ter 11 dígitos (DDD + 9 + 8 dígitos)\n        // ou 13 dígitos com código do país (55 + DDD + 9 + 8 dígitos)\n        return apenasNumeros.length === 11 || apenasNumeros.length === 13 && apenasNumeros.startsWith('55');\n    }\n    /**\r\n   * Gerar template de SMS baseado no tipo de notificação\r\n   */ gerarTemplateSMS(tipo, contexto) {\n        switch(tipo){\n            case 'novo_agendamento':\n                return `ServiceTech: Seu agendamento de ${contexto.servico_nome} foi solicitado para ${this.formatarDataSMS(contexto.data_hora_inicio)} em ${contexto.empresa_nome}. Aguarde confirmação.`;\n            case 'agendamento_confirmado':\n                return `ServiceTech: Agendamento confirmado! ${contexto.servico_nome} em ${contexto.empresa_nome} no dia ${this.formatarDataSMS(contexto.data_hora_inicio)}. Código: ${contexto.codigo_confirmacao}`;\n            case 'agendamento_recusado':\n                return `ServiceTech: Seu agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} foi recusado. Tente outro horário ou estabelecimento.`;\n            case 'agendamento_cancelado':\n                return `ServiceTech: Agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} foi cancelado. Reembolso processado automaticamente.`;\n            case 'lembrete_confirmacao':\n                return `ServiceTech: Lembrete! Agendamento de ${contexto.cliente_nome} para ${contexto.servico_nome} expira em breve. Confirme ou recuse.`;\n            case 'lembrete_agendamento':\n                return `ServiceTech: Lembrete! Seu agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} é amanhã às ${this.formatarHoraSMS(contexto.data_hora_inicio)}.`;\n            case 'pagamento_confirmado':\n                return `ServiceTech: Pagamento confirmado! Agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} para ${this.formatarDataSMS(contexto.data_hora_inicio)}.`;\n            default:\n                return `ServiceTech: Você tem uma nova notificação. Acesse o app para mais detalhes.`;\n        }\n    }\n    /**\r\n   * Formatar data para SMS (formato brasileiro)\r\n   */ formatarDataSMS(dataISO) {\n        const data = new Date(dataISO);\n        return data.toLocaleDateString('pt-BR', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }\n    /**\r\n   * Formatar hora para SMS\r\n   */ formatarHoraSMS(dataISO) {\n        const data = new Date(dataISO);\n        return data.toLocaleTimeString('pt-BR', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/SMSService.ts\n");

/***/ }),

/***/ "(rsc)/./src/templates/email/EmailTemplates.ts":
/*!***********************************************!*\
  !*** ./src/templates/email/EmailTemplates.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! date-fns/locale */ \"(rsc)/./node_modules/date-fns/locale/pt-BR.js\");\n\n\nclass EmailTemplates {\n    /**\r\n   * Template base para emails\r\n   */ baseTemplate(title, content) {\n        return `\n<!DOCTYPE html>\n<html lang=\"pt-BR\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${title}</title>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }\n        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { text-align: center; padding: 20px 0; border-bottom: 2px solid #3b82f6; margin-bottom: 30px; }\n        .logo { font-size: 24px; font-weight: bold; color: #3b82f6; }\n        .content { padding: 20px 0; }\n        .highlight { background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0; }\n        .button { display: inline-block; padding: 12px 24px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 6px; margin: 10px 5px; }\n        .button:hover { background-color: #2563eb; }\n        .footer { text-align: center; padding: 20px 0; border-top: 1px solid #e5e7eb; margin-top: 30px; color: #6b7280; font-size: 14px; }\n        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 15px 0; }\n        .info-item { padding: 8px; background-color: #f9fafb; border-radius: 4px; }\n        .info-label { font-weight: bold; color: #374151; }\n        .info-value { color: #6b7280; }\n        @media (max-width: 600px) { .info-grid { grid-template-columns: 1fr; } }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <div class=\"logo\">ServiceTech</div>\n            <p style=\"margin: 5px 0; color: #6b7280;\">Plataforma de Agendamento de Serviços</p>\n        </div>\n        <div class=\"content\">\n            ${content}\n        </div>\n        <div class=\"footer\">\n            <p>Este é um email automático, não responda a esta mensagem.</p>\n            <p>© 2025 ServiceTech. Todos os direitos reservados.</p>\n        </div>\n    </div>\n</body>\n</html>`;\n    }\n    /**\r\n   * Formatar data para exibição\r\n   */ formatarData(dataString) {\n        const data = new Date(dataString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_0__.format)(data, \"dd/MM/yyyy 'às' HH:mm\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_1__.ptBR\n        });\n    }\n    /**\r\n   * Formatar valor monetário\r\n   */ formatarValor(valor) {\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(valor);\n    }\n    /**\r\n   * Template: Novo agendamento para cliente\r\n   */ novoAgendamentoCliente(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const valorFormatado = this.formatarValor(contexto.valor_total);\n        const content = `\n      <h2 style=\"color: #3b82f6; margin-bottom: 20px;\">Agendamento Solicitado com Sucesso! 🎉</h2>\n      \n      <p>Olá! Seu agendamento foi solicitado com sucesso e está aguardando confirmação do estabelecimento.</p>\n      \n      <div class=\"highlight\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Detalhes do Agendamento</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Estabelecimento:</div>\n            <div class=\"info-value\">${contexto.empresa_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Serviço:</div>\n            <div class=\"info-value\">${contexto.servico_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Profissional:</div>\n            <div class=\"info-value\">${contexto.colaborador_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data e Horário:</div>\n            <div class=\"info-value\">${dataFormatada}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Valor:</div>\n            <div class=\"info-value\">${valorFormatado}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Forma de Pagamento:</div>\n            <div class=\"info-value\">${contexto.forma_pagamento}</div>\n          </div>\n        </div>\n        \n        <p style=\"margin: 15px 0 5px 0;\"><strong>Código de Confirmação:</strong> <span style=\"font-size: 18px; color: #3b82f6; font-weight: bold;\">${contexto.codigo_confirmacao}</span></p>\n      </div>\n\n      ${contexto.observacoes_cliente ? `\n        <div style=\"background-color: #fef3c7; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;\">\n          <strong>Suas observações:</strong><br>\n          ${contexto.observacoes_cliente}\n        </div>\n      ` : ''}\n\n      <p><strong>⏰ Importante:</strong> O estabelecimento tem até <strong>24 horas</strong> para confirmar seu agendamento. Você receberá uma notificação assim que houver uma resposta.</p>\n      \n      <p>Endereço: ${contexto.empresa_endereco}</p>\n    `;\n        return {\n            subject: `Agendamento Solicitado - ${contexto.servico_nome} em ${contexto.empresa_nome}`,\n            html: this.baseTemplate('Agendamento Solicitado', content),\n            text: `Agendamento solicitado com sucesso!\\n\\nServiço: ${contexto.servico_nome}\\nEstabelecimento: ${contexto.empresa_nome}\\nData: ${dataFormatada}\\nValor: ${valorFormatado}\\nCódigo: ${contexto.codigo_confirmacao}`\n        };\n    }\n    /**\r\n   * Template: Agendamento confirmado\r\n   */ agendamentoConfirmado(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const valorFormatado = this.formatarValor(contexto.valor_total);\n        const content = `\n      <h2 style=\"color: #10b981; margin-bottom: 20px;\">Agendamento Confirmado! ✅</h2>\n      \n      <p>Ótima notícia! Seu agendamento foi <strong>confirmado</strong> pelo estabelecimento.</p>\n      \n      <div class=\"highlight\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Detalhes Confirmados</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Estabelecimento:</div>\n            <div class=\"info-value\">${contexto.empresa_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Serviço:</div>\n            <div class=\"info-value\">${contexto.servico_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Profissional:</div>\n            <div class=\"info-value\">${contexto.colaborador_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data e Horário:</div>\n            <div class=\"info-value\">${dataFormatada}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Valor:</div>\n            <div class=\"info-value\">${valorFormatado}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Código:</div>\n            <div class=\"info-value\">${contexto.codigo_confirmacao}</div>\n          </div>\n        </div>\n      </div>\n\n      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>\n      \n      <p><strong>💡 Dica:</strong> Chegue alguns minutos antes do horário agendado. Em caso de imprevistos, entre em contato diretamente com o estabelecimento.</p>\n      \n      <p>Aguardamos você! 😊</p>\n    `;\n        return {\n            subject: `✅ Agendamento Confirmado - ${contexto.servico_nome}`,\n            html: this.baseTemplate('Agendamento Confirmado', content),\n            text: `Agendamento confirmado!\\n\\nServiço: ${contexto.servico_nome}\\nEstabelecimento: ${contexto.empresa_nome}\\nData: ${dataFormatada}\\nEndereço: ${contexto.empresa_endereco}`\n        };\n    }\n    /**\r\n   * Template: Agendamento recusado\r\n   */ agendamentoRecusado(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const content = `\n      <h2 style=\"color: #ef4444; margin-bottom: 20px;\">Agendamento Não Confirmado 😔</h2>\n      \n      <p>Infelizmente, o estabelecimento não pôde confirmar seu agendamento para o horário solicitado.</p>\n      \n      <div style=\"background-color: #fef2f2; padding: 15px; border-radius: 6px; border-left: 4px solid #ef4444; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Agendamento Recusado</h3>\n        <p><strong>Serviço:</strong> ${contexto.servico_nome}</p>\n        <p><strong>Data/Horário:</strong> ${dataFormatada}</p>\n        <p><strong>Estabelecimento:</strong> ${contexto.empresa_nome}</p>\n        <p><strong>Código:</strong> ${contexto.codigo_confirmacao}</p>\n      </div>\n\n      ${contexto.forma_pagamento === 'Online' ? `\n        <div style=\"background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;\">\n          <p><strong>💳 Reembolso:</strong> Como você pagou online, o reembolso será processado automaticamente em até 5 dias úteis.</p>\n        </div>\n      ` : ''}\n\n      <p><strong>🔄 Não desista!</strong> Tente agendar para outro horário ou explore outros estabelecimentos disponíveis em nossa plataforma.</p>\n      \n      <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/buscar\" class=\"button\">Buscar Outros Horários</a>\n    `;\n        return {\n            subject: `❌ Agendamento Não Confirmado - ${contexto.servico_nome}`,\n            html: this.baseTemplate('Agendamento Não Confirmado', content),\n            text: `Agendamento não confirmado.\\n\\nServiço: ${contexto.servico_nome}\\nData: ${dataFormatada}\\nEstabelecimento: ${contexto.empresa_nome}\\n\\nTente agendar para outro horário.`\n        };\n    }\n    /**\r\n   * Template: Agendamento cancelado\r\n   */ agendamentoCancelado(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const content = `\n      <h2 style=\"color: #f59e0b; margin-bottom: 20px;\">Agendamento Cancelado ⚠️</h2>\n      \n      <p>Seu agendamento foi cancelado conforme solicitado.</p>\n      \n      <div style=\"background-color: #fffbeb; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Detalhes do Cancelamento</h3>\n        <p><strong>Serviço:</strong> ${contexto.servico_nome}</p>\n        <p><strong>Data/Horário:</strong> ${dataFormatada}</p>\n        <p><strong>Estabelecimento:</strong> ${contexto.empresa_nome}</p>\n        <p><strong>Código:</strong> ${contexto.codigo_confirmacao}</p>\n      </div>\n\n      ${contexto.forma_pagamento === 'Online' ? `\n        <div style=\"background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;\">\n          <p><strong>💳 Reembolso:</strong> O reembolso será processado de acordo com a política de cancelamento do estabelecimento.</p>\n        </div>\n      ` : ''}\n\n      <p>Esperamos vê-lo novamente em breve! 😊</p>\n      \n      <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/buscar\" class=\"button\">Fazer Novo Agendamento</a>\n    `;\n        return {\n            subject: `🚫 Agendamento Cancelado - ${contexto.servico_nome}`,\n            html: this.baseTemplate('Agendamento Cancelado', content),\n            text: `Agendamento cancelado.\\n\\nServiço: ${contexto.servico_nome}\\nData: ${dataFormatada}\\nEstabelecimento: ${contexto.empresa_nome}`\n        };\n    }\n    /**\r\n   * Template: Lembrete de confirmação para proprietário/colaborador\r\n   */ lembreteConfirmacao(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const prazoFormatado = this.formatarData(contexto.prazo_confirmacao);\n        const valorFormatado = this.formatarValor(contexto.valor_total);\n        const content = `\n      <h2 style=\"color: #f59e0b; margin-bottom: 20px;\">⏰ Lembrete: Agendamento Pendente</h2>\n      \n      <p>Você tem um agendamento aguardando confirmação que expira em breve!</p>\n      \n      <div style=\"background-color: #fffbeb; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Detalhes do Agendamento</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Cliente:</div>\n            <div class=\"info-value\">${contexto.cliente_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Serviço:</div>\n            <div class=\"info-value\">${contexto.servico_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Profissional:</div>\n            <div class=\"info-value\">${contexto.colaborador_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data e Horário:</div>\n            <div class=\"info-value\">${dataFormatada}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Valor:</div>\n            <div class=\"info-value\">${valorFormatado}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Prazo Limite:</div>\n            <div class=\"info-value\">${prazoFormatado}</div>\n          </div>\n        </div>\n        \n        <p style=\"margin: 15px 0 5px 0;\"><strong>Código:</strong> <span style=\"font-size: 18px; color: #f59e0b; font-weight: bold;\">${contexto.codigo_confirmacao}</span></p>\n      </div>\n\n      ${contexto.observacoes_cliente ? `\n        <div style=\"background-color: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n          <strong>Observações do cliente:</strong><br>\n          ${contexto.observacoes_cliente}\n        </div>\n      ` : ''}\n\n      <p><strong>⚠️ Ação Necessária:</strong> Confirme ou recuse este agendamento antes do prazo limite para evitar cancelamento automático.</p>\n      \n      <div style=\"text-align: center; margin: 30px 0;\">\n        <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/proprietario/agendamentos\" class=\"button\" style=\"background-color: #10b981;\">✅ Confirmar</a>\n        <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/proprietario/agendamentos\" class=\"button\" style=\"background-color: #ef4444;\">❌ Recusar</a>\n      </div>\n    `;\n        return {\n            subject: `⏰ Lembrete: Agendamento de ${contexto.cliente_nome} expira em breve`,\n            html: this.baseTemplate('Lembrete de Confirmação', content),\n            text: `Lembrete: Agendamento pendente!\\n\\nCliente: ${contexto.cliente_nome}\\nServiço: ${contexto.servico_nome}\\nData: ${dataFormatada}\\nPrazo: ${prazoFormatado}\\n\\nConfirme ou recuse antes do prazo limite.`\n        };\n    }\n    /**\r\n   * Template: Lembrete de agendamento para cliente\r\n   */ lembreteAgendamento(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const valorFormatado = this.formatarValor(contexto.valor_total);\n        const content = `\n      <h2 style=\"color: #3b82f6; margin-bottom: 20px;\">🔔 Lembrete: Seu Agendamento é Amanhã!</h2>\n      \n      <p>Este é um lembrete amigável sobre seu agendamento confirmado para amanhã.</p>\n      \n      <div class=\"highlight\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">Detalhes do Agendamento</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Estabelecimento:</div>\n            <div class=\"info-value\">${contexto.empresa_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Serviço:</div>\n            <div class=\"info-value\">${contexto.servico_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Profissional:</div>\n            <div class=\"info-value\">${contexto.colaborador_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data e Horário:</div>\n            <div class=\"info-value\">${dataFormatada}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Valor:</div>\n            <div class=\"info-value\">${valorFormatado}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Código:</div>\n            <div class=\"info-value\">${contexto.codigo_confirmacao}</div>\n          </div>\n        </div>\n      </div>\n\n      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>\n      \n      <div style=\"background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;\">\n        <p><strong>💡 Dicas importantes:</strong></p>\n        <ul style=\"margin: 10px 0; padding-left: 20px;\">\n          <li>Chegue 10 minutos antes do horário</li>\n          <li>Traga um documento de identificação</li>\n          <li>Em caso de imprevistos, entre em contato com o estabelecimento</li>\n        </ul>\n      </div>\n\n      <p>Estamos ansiosos para atendê-lo! 😊</p>\n    `;\n        return {\n            subject: `🔔 Lembrete: ${contexto.servico_nome} amanhã às ${(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_0__.format)(new Date(contexto.data_hora_inicio), 'HH:mm')}`,\n            html: this.baseTemplate('Lembrete de Agendamento', content),\n            text: `Lembrete: Seu agendamento é amanhã!\\n\\nServiço: ${contexto.servico_nome}\\nEstabelecimento: ${contexto.empresa_nome}\\nData: ${dataFormatada}\\nEndereço: ${contexto.empresa_endereco}`\n        };\n    }\n    /**\r\n   * Template: Pagamento confirmado\r\n   */ pagamentoConfirmado(contexto) {\n        const dataFormatada = this.formatarData(contexto.data_hora_inicio);\n        const valorFormatado = this.formatarValor(contexto.valor_total);\n        const content = `\n      <h2 style=\"color: #10b981; margin-bottom: 20px;\">💳 Pagamento Confirmado! ✅</h2>\n\n      <p>Excelente! Seu pagamento foi processado com sucesso e seu agendamento está confirmado.</p>\n\n      <div style=\"background-color: #f0fdf4; padding: 15px; border-radius: 6px; border-left: 4px solid #10b981; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">💰 Comprovante de Pagamento</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Valor Pago:</div>\n            <div class=\"info-value\" style=\"color: #10b981; font-weight: bold;\">${valorFormatado}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Forma de Pagamento:</div>\n            <div class=\"info-value\">Online</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Status:</div>\n            <div class=\"info-value\" style=\"color: #10b981; font-weight: bold;\">✅ Pago</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data do Pagamento:</div>\n            <div class=\"info-value\">${(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_0__.format)(new Date(), \"dd/MM/yyyy 'às' HH:mm\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_1__.ptBR\n        })}</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"highlight\">\n        <h3 style=\"margin-top: 0; color: #1f2937;\">📅 Detalhes do Agendamento</h3>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">Estabelecimento:</div>\n            <div class=\"info-value\">${contexto.empresa_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Serviço:</div>\n            <div class=\"info-value\">${contexto.servico_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Profissional:</div>\n            <div class=\"info-value\">${contexto.colaborador_nome}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Data e Horário:</div>\n            <div class=\"info-value\">${dataFormatada}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">Código:</div>\n            <div class=\"info-value\">${contexto.codigo_confirmacao}</div>\n          </div>\n        </div>\n      </div>\n\n      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>\n\n      <div style=\"background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;\">\n        <p><strong>🎯 Próximos passos:</strong></p>\n        <ul style=\"margin: 10px 0; padding-left: 20px;\">\n          <li>Seu agendamento está confirmado e pago</li>\n          <li>Chegue 10 minutos antes do horário</li>\n          <li>Apresente o código de confirmação no local</li>\n          <li>Guarde este email como comprovante</li>\n        </ul>\n      </div>\n\n      <p>Obrigado por escolher nossos serviços! 😊</p>\n    `;\n        return {\n            subject: `💳 Pagamento Confirmado - ${contexto.servico_nome} em ${contexto.empresa_nome}`,\n            html: this.baseTemplate('Pagamento Confirmado', content),\n            text: `Pagamento confirmado!\\n\\nValor: ${valorFormatado}\\nServiço: ${contexto.servico_nome}\\nEstabelecimento: ${contexto.empresa_nome}\\nData: ${dataFormatada}\\nCódigo: ${contexto.codigo_confirmacao}`\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/templates/email/EmailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/notificationHelpers.ts":
/*!******************************************!*\
  !*** ./src/utils/notificationHelpers.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extrairContextoAgendamento: () => (/* binding */ extrairContextoAgendamento),\n/* harmony export */   notificarCancelamentoAgendamento: () => (/* binding */ notificarCancelamentoAgendamento),\n/* harmony export */   notificarConfirmacaoAgendamento: () => (/* binding */ notificarConfirmacaoAgendamento),\n/* harmony export */   notificarNovoAgendamento: () => (/* binding */ notificarNovoAgendamento),\n/* harmony export */   notificarPagamentoConfirmado: () => (/* binding */ notificarPagamentoConfirmado),\n/* harmony export */   notificarRecusaAgendamento: () => (/* binding */ notificarRecusaAgendamento)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n/* harmony import */ var _services_NotificationService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/NotificationService */ \"(rsc)/./src/services/NotificationService.ts\");\n\n\n/**\r\n * Extrai contexto completo de um agendamento para notificações\r\n */ async function extrairContextoAgendamento(agendamentoId) {\n    try {\n        const supabase = (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createAdminClient)();\n        // Buscar dados completos do agendamento com joins\n        const { data: agendamento, error } = await supabase.from('agendamentos').select(`\n        agendamento_id,\n        empresa_id,\n        cliente_user_id,\n        colaborador_user_id,\n        codigo_confirmacao,\n        data_hora_inicio,\n        data_hora_fim,\n        forma_pagamento,\n        valor_total,\n        observacoes_cliente,\n        prazo_confirmacao,\n        servico:servicos(nome_servico, preco),\n        empresa:empresas(nome_empresa, endereco_completo),\n        cliente:auth.users!agendamentos_cliente_user_id_fkey(id, email, raw_user_meta_data),\n        colaborador:auth.users!agendamentos_colaborador_user_id_fkey(id, email, raw_user_meta_data)\n      `).eq('agendamento_id', agendamentoId).single();\n        if (error || !agendamento) {\n            console.error('❌ Erro ao buscar agendamento para notificação:', error);\n            return null;\n        }\n        // Buscar proprietário da empresa\n        const { data: colaboradorEmpresa, error: colaboradorError } = await supabase.from('colaboradores_empresa').select(`\n        user_id,\n        papel,\n        auth.users!colaboradores_empresa_user_id_fkey(id, email, raw_user_meta_data)\n      `).eq('empresa_id', agendamento.empresa_id).eq('papel', 'Proprietario').eq('ativo', true).single();\n        if (colaboradorError || !colaboradorEmpresa) {\n            console.error('❌ Erro ao buscar proprietário da empresa:', colaboradorError);\n            return null;\n        }\n        // Extrair nomes dos metadados\n        const clienteNome = agendamento.cliente?.raw_user_meta_data?.name || agendamento.cliente?.raw_user_meta_data?.full_name || 'Cliente';\n        const colaboradorNome = agendamento.colaborador?.raw_user_meta_data?.name || agendamento.colaborador?.raw_user_meta_data?.full_name || 'Profissional';\n        const clienteTelefone = agendamento.cliente?.raw_user_meta_data?.phone || '';\n        // Contexto para o cliente\n        const contextoCliente = {\n            agendamento_id: agendamento.agendamento_id,\n            codigo_confirmacao: agendamento.codigo_confirmacao,\n            cliente_nome: clienteNome,\n            cliente_email: agendamento.cliente.email,\n            empresa_nome: agendamento.empresa.nome_empresa,\n            empresa_endereco: agendamento.empresa.endereco_completo,\n            servico_nome: agendamento.servico.nome_servico,\n            servico_preco: agendamento.servico.preco,\n            colaborador_nome: colaboradorNome,\n            data_hora_inicio: agendamento.data_hora_inicio,\n            data_hora_fim: agendamento.data_hora_fim,\n            forma_pagamento: agendamento.forma_pagamento,\n            valor_total: agendamento.valor_total,\n            observacoes_cliente: agendamento.observacoes_cliente,\n            prazo_confirmacao: agendamento.prazo_confirmacao\n        };\n        // Contexto para o proprietário/colaborador\n        const contextoProprietario = {\n            agendamento_id: agendamento.agendamento_id,\n            codigo_confirmacao: agendamento.codigo_confirmacao,\n            cliente_nome: clienteNome,\n            cliente_telefone: clienteTelefone,\n            servico_nome: agendamento.servico.nome_servico,\n            colaborador_nome: colaboradorNome,\n            data_hora_inicio: agendamento.data_hora_inicio,\n            data_hora_fim: agendamento.data_hora_fim,\n            forma_pagamento: agendamento.forma_pagamento,\n            valor_total: agendamento.valor_total,\n            observacoes_cliente: agendamento.observacoes_cliente,\n            prazo_confirmacao: agendamento.prazo_confirmacao\n        };\n        return {\n            contextoCliente,\n            contextoProprietario,\n            clienteId: agendamento.cliente_user_id,\n            proprietarioId: colaboradorEmpresa.user_id,\n            colaboradorId: agendamento.colaborador_user_id,\n            empresaId: agendamento.empresa_id\n        };\n    } catch (error) {\n        console.error('❌ Erro ao extrair contexto do agendamento:', error);\n        return null;\n    }\n}\n/**\r\n * Envia notificação de novo agendamento para cliente e proprietário\r\n */ async function notificarNovoAgendamento(agendamentoId) {\n    try {\n        const contexto = await extrairContextoAgendamento(agendamentoId);\n        if (!contexto) {\n            console.error('❌ Não foi possível extrair contexto do agendamento');\n            return false;\n        }\n        // Enviar notificação para o cliente\n        const responseCliente = await fetch('/api/notifications', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                tipo: 'novo_agendamento',\n                destinatario_id: contexto.clienteId,\n                contexto: contexto.contextoCliente,\n                canal: 'email',\n                agendamento_id: agendamentoId,\n                empresa_id: contexto.empresaId\n            })\n        });\n        // Enviar notificação para o proprietário (sobre novo agendamento pendente)\n        const responseProprietario = await fetch('/api/notifications', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                tipo: 'lembrete_confirmacao',\n                destinatario_id: contexto.proprietarioId,\n                contexto: contexto.contextoProprietario,\n                canal: 'email',\n                agendamento_id: agendamentoId,\n                empresa_id: contexto.empresaId\n            })\n        });\n        const resultadoCliente = await responseCliente.json();\n        const resultadoProprietario = await responseProprietario.json();\n        const sucessoCliente = responseCliente.ok && resultadoCliente.success;\n        const sucessoProprietario = responseProprietario.ok && resultadoProprietario.success;\n        if (sucessoCliente && sucessoProprietario) {\n            console.log('✅ Notificações de novo agendamento enviadas com sucesso');\n            return true;\n        } else {\n            console.error('❌ Erro ao enviar algumas notificações:', {\n                cliente: sucessoCliente ? 'OK' : resultadoCliente.error,\n                proprietario: sucessoProprietario ? 'OK' : resultadoProprietario.error\n            });\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Erro ao notificar novo agendamento:', error);\n        return false;\n    }\n}\n/**\r\n * Envia notificação de confirmação de agendamento\r\n */ async function notificarConfirmacaoAgendamento(agendamentoId) {\n    try {\n        const contexto = await extrairContextoAgendamento(agendamentoId);\n        if (!contexto) {\n            console.error('❌ Não foi possível extrair contexto do agendamento');\n            return false;\n        }\n        // Enviar notificação para o cliente\n        const response = await fetch('/api/notifications', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                tipo: 'agendamento_confirmado',\n                destinatario_id: contexto.clienteId,\n                contexto: contexto.contextoCliente,\n                canal: 'email',\n                agendamento_id: agendamentoId\n            })\n        });\n        const resultado = await response.json();\n        if (response.ok && resultado.success) {\n            console.log('✅ Notificação de confirmação enviada com sucesso');\n            return true;\n        } else {\n            console.error('❌ Erro ao enviar notificação de confirmação:', resultado.error);\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Erro ao notificar confirmação de agendamento:', error);\n        return false;\n    }\n}\n/**\r\n * Envia notificação de recusa de agendamento\r\n */ async function notificarRecusaAgendamento(agendamentoId) {\n    try {\n        const contexto = await extrairContextoAgendamento(agendamentoId);\n        if (!contexto) {\n            console.error('❌ Não foi possível extrair contexto do agendamento');\n            return false;\n        }\n        // Enviar notificação para o cliente\n        const response = await fetch('/api/notifications', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                tipo: 'agendamento_recusado',\n                destinatario_id: contexto.clienteId,\n                contexto: contexto.contextoCliente,\n                canal: 'email',\n                agendamento_id: agendamentoId\n            })\n        });\n        const resultado = await response.json();\n        if (response.ok && resultado.success) {\n            console.log('✅ Notificação de recusa enviada com sucesso');\n            return true;\n        } else {\n            console.error('❌ Erro ao enviar notificação de recusa:', resultado.error);\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Erro ao notificar recusa de agendamento:', error);\n        return false;\n    }\n}\n/**\r\n * Envia notificação de cancelamento de agendamento\r\n */ async function notificarCancelamentoAgendamento(agendamentoId) {\n    try {\n        const contexto = await extrairContextoAgendamento(agendamentoId);\n        if (!contexto) {\n            console.error('❌ Não foi possível extrair contexto do agendamento');\n            return false;\n        }\n        // Enviar notificação para o cliente\n        const response = await fetch('/api/notifications', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                tipo: 'agendamento_cancelado',\n                destinatario_id: contexto.clienteId,\n                contexto: contexto.contextoCliente,\n                canal: 'email',\n                agendamento_id: agendamentoId\n            })\n        });\n        const resultado = await response.json();\n        if (response.ok && resultado.success) {\n            console.log('✅ Notificação de cancelamento enviada com sucesso');\n            return true;\n        } else {\n            console.error('❌ Erro ao enviar notificação de cancelamento:', resultado.error);\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Erro ao notificar cancelamento de agendamento:', error);\n        return false;\n    }\n}\n/**\r\n * Envia notificação de pagamento confirmado\r\n */ async function notificarPagamentoConfirmado(agendamentoId) {\n    try {\n        const contexto = await extrairContextoAgendamento(agendamentoId);\n        if (!contexto) {\n            console.error('❌ Não foi possível extrair contexto do agendamento');\n            return false;\n        }\n        const { contextoCliente, contextoProprietario, clienteId, proprietarioId } = contexto;\n        // Enviar notificação para o cliente\n        const notificationService = new _services_NotificationService__WEBPACK_IMPORTED_MODULE_1__.NotificationService();\n        const resultadoCliente = await notificationService.processarNotificacao({\n            destinatario_id: clienteId,\n            tipo: 'pagamento_confirmado',\n            contexto: contextoCliente,\n            canal: 'email',\n            agendamento_id: agendamentoId\n        });\n        // Enviar notificação para o proprietário\n        const resultadoProprietario = await notificationService.processarNotificacao({\n            destinatario_id: proprietarioId,\n            tipo: 'pagamento_confirmado',\n            contexto: contextoProprietario,\n            canal: 'email',\n            agendamento_id: agendamentoId\n        });\n        if (resultadoCliente.success && resultadoProprietario.success) {\n            console.log(`✅ Notificações de pagamento confirmado enviadas para agendamento ${agendamentoId}`);\n            return true;\n        } else {\n            console.error('❌ Erro ao enviar notificações de pagamento confirmado');\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Erro ao notificar pagamento confirmado:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/notificationHelpers.ts\n");

/***/ })

};
;