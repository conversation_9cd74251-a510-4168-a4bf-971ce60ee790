"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_roundRobin_ts";
exports.ids = ["_rsc_src_utils_roundRobin_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/roundRobin.ts":
/*!*********************************!*\
  !*** ./src/utils/roundRobin.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buscarEstatisticasColaboradores: () => (/* binding */ buscarEstatisticasColaboradores),\n/* harmony export */   calcularEstatisticasDistribuicao: () => (/* binding */ calcularEstatisticasDistribuicao),\n/* harmony export */   selecionarColaboradorRoundRobin: () => (/* binding */ selecionarColaboradorRoundRobin),\n/* harmony export */   selecionarProximoColaborador: () => (/* binding */ selecionarProximoColaborador)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/supabase/client */ \"(rsc)/./src/utils/supabase/client.ts\");\n/**\r\n * Utilitários para lógica de Round-Robin\r\n * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin\r\n * \r\n * Este módulo implementa um algoritmo de round-robin inteligente que:\r\n * 1. Distribui agendamentos igualmente entre colaboradores\r\n * 2. Considera o histórico de agendamentos recentes\r\n * 3. Prioriza colaboradores menos utilizados\r\n * 4. Mantém estatísticas de distribuição\r\n */ \n/**\r\n * Configuração padrão para o algoritmo round-robin\r\n */ const CONFIGURACAO_PADRAO = {\n    periodo_dias: 30,\n    apenas_confirmados: false,\n    peso_temporal: 1,\n    priorizar_inativos: true\n};\n/**\r\n * Busca estatísticas de agendamentos dos colaboradores\r\n */ async function buscarEstatisticasColaboradores(empresa_id, servico_id, colaboradores_ids, configuracao = {}) {\n    const config = {\n        ...CONFIGURACAO_PADRAO,\n        ...configuracao\n    };\n    const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    // Calcular data limite baseada no período\n    const dataLimite = new Date();\n    dataLimite.setDate(dataLimite.getDate() - config.periodo_dias);\n    try {\n        // Buscar dados dos colaboradores\n        const { data: colaboradores, error: colaboradoresError } = await supabase.from('colaboradores_empresa').select(`\n        colaborador_user_id,\n        auth_users:colaborador_user_id (\n          raw_user_meta_data\n        )\n      `).eq('empresa_id', empresa_id).in('colaborador_user_id', colaboradores_ids).eq('ativo', true).eq('ativo_como_prestador', true);\n        if (colaboradoresError) {\n            console.error('Erro ao buscar colaboradores:', colaboradoresError);\n            throw new Error('Erro ao buscar dados dos colaboradores');\n        }\n        // Buscar estatísticas de agendamentos\n        const statusFiltro = config.apenas_confirmados ? [\n            'Confirmado'\n        ] : [\n            'Pendente',\n            'Confirmado'\n        ];\n        const { data: agendamentos, error: agendamentosError } = await supabase.from('agendamentos').select('colaborador_user_id, created_at').eq('empresa_id', empresa_id).eq('servico_id', servico_id).in('colaborador_user_id', colaboradores_ids).in('status_agendamento', statusFiltro).gte('created_at', dataLimite.toISOString()).order('created_at', {\n            ascending: false\n        });\n        if (agendamentosError) {\n            console.error('Erro ao buscar agendamentos:', agendamentosError);\n            throw new Error('Erro ao buscar histórico de agendamentos');\n        }\n        // Processar dados dos colaboradores\n        const colaboradoresRoundRobin = colaboradores.map((colaborador)=>{\n            const agendamentosColaborador = agendamentos?.filter((a)=>a.colaborador_user_id === colaborador.colaborador_user_id) || [];\n            const userData = colaborador.auth_users?.raw_user_meta_data || {};\n            return {\n                colaborador_user_id: colaborador.colaborador_user_id,\n                name: userData.name || 'Colaborador',\n                email: userData.email || '',\n                total_agendamentos: agendamentosColaborador.length,\n                ultimo_agendamento: agendamentosColaborador.length > 0 ? agendamentosColaborador[0].created_at : undefined\n            };\n        });\n        return colaboradoresRoundRobin;\n    } catch (error) {\n        console.error('Erro ao buscar estatísticas de colaboradores:', error);\n        throw error;\n    }\n}\n/**\r\n * Calcula estatísticas de distribuição de agendamentos\r\n */ function calcularEstatisticasDistribuicao(colaboradores) {\n    const totalAgendamentos = colaboradores.reduce((sum, c)=>sum + c.total_agendamentos, 0);\n    const distribuicao = colaboradores.map((colaborador)=>({\n            colaborador_user_id: colaborador.colaborador_user_id,\n            name: colaborador.name,\n            count: colaborador.total_agendamentos,\n            percentual: totalAgendamentos > 0 ? colaborador.total_agendamentos / totalAgendamentos * 100 : 0\n        }));\n    // Encontrar colaboradores com mais e menos agendamentos\n    const ordenados = [\n        ...colaboradores\n    ].sort((a, b)=>a.total_agendamentos - b.total_agendamentos);\n    const menosUtilizado = ordenados[0];\n    const maisUtilizado = ordenados[ordenados.length - 1];\n    return {\n        total_colaboradores: colaboradores.length,\n        total_agendamentos: totalAgendamentos,\n        distribuicao,\n        colaborador_menos_utilizado: menosUtilizado.colaborador_user_id,\n        colaborador_mais_utilizado: maisUtilizado.colaborador_user_id,\n        diferenca_maxima: maisUtilizado.total_agendamentos - menosUtilizado.total_agendamentos\n    };\n}\n/**\r\n * Seleciona o próximo colaborador usando algoritmo round-robin\r\n */ function selecionarProximoColaborador(colaboradores, configuracao = {}) {\n    const config = {\n        ...CONFIGURACAO_PADRAO,\n        ...configuracao\n    };\n    if (colaboradores.length === 0) {\n        throw new Error('Nenhum colaborador disponível para seleção');\n    }\n    if (colaboradores.length === 1) {\n        return {\n            colaborador_selecionado: colaboradores[0].colaborador_user_id,\n            motivo_selecao: 'ultimo_disponivel',\n            estatisticas_antes: calcularEstatisticasDistribuicao(colaboradores)\n        };\n    }\n    const estatisticasAntes = calcularEstatisticasDistribuicao(colaboradores);\n    // Algoritmo Round-Robin: selecionar colaborador com menos agendamentos\n    const colaboradoresOrdenados = [\n        ...colaboradores\n    ].sort((a, b)=>{\n        // Primeiro critério: número de agendamentos (menor primeiro)\n        if (a.total_agendamentos !== b.total_agendamentos) {\n            return a.total_agendamentos - b.total_agendamentos;\n        }\n        // Segundo critério: priorizar quem não teve agendamentos recentes\n        if (config.priorizar_inativos) {\n            if (!a.ultimo_agendamento && b.ultimo_agendamento) return -1;\n            if (a.ultimo_agendamento && !b.ultimo_agendamento) return 1;\n            if (a.ultimo_agendamento && b.ultimo_agendamento) {\n                // Quem teve agendamento mais antigo tem prioridade\n                return new Date(a.ultimo_agendamento).getTime() - new Date(b.ultimo_agendamento).getTime();\n            }\n        }\n        // Terceiro critério: ordem alfabética para consistência\n        return a.name.localeCompare(b.name);\n    });\n    const colaboradorSelecionado = colaboradoresOrdenados[0];\n    // Simular estatísticas após a seleção\n    const colaboradoresDepois = colaboradores.map((c)=>c.colaborador_user_id === colaboradorSelecionado.colaborador_user_id ? {\n            ...c,\n            total_agendamentos: c.total_agendamentos + 1\n        } : c);\n    return {\n        colaborador_selecionado: colaboradorSelecionado.colaborador_user_id,\n        motivo_selecao: 'round_robin',\n        estatisticas_antes: estatisticasAntes,\n        estatisticas_depois: calcularEstatisticasDistribuicao(colaboradoresDepois)\n    };\n}\n/**\r\n * Função principal para seleção de colaborador via round-robin\r\n * Esta é a função que será chamada pela API de agendamentos\r\n */ async function selecionarColaboradorRoundRobin(parametros, configuracao = {}) {\n    try {\n        // Buscar estatísticas atualizadas dos colaboradores\n        const colaboradoresIds = parametros.colaboradores_disponiveis.map((c)=>c.colaborador_user_id);\n        const colaboradoresComEstatisticas = await buscarEstatisticasColaboradores(parametros.empresa_id, parametros.servico_id, colaboradoresIds, configuracao);\n        // Selecionar próximo colaborador\n        const resultado = selecionarProximoColaborador(colaboradoresComEstatisticas, configuracao);\n        console.log('🎯 Round-Robin - Colaborador selecionado:', {\n            colaborador_id: resultado.colaborador_selecionado,\n            motivo: resultado.motivo_selecao,\n            estatisticas: resultado.estatisticas_antes\n        });\n        return resultado;\n    } catch (error) {\n        console.error('❌ Erro na seleção round-robin:', error);\n        // Fallback: seleção aleatória se houver erro\n        const indiceAleatorio = Math.floor(Math.random() * parametros.colaboradores_disponiveis.length);\n        const colaboradorFallback = parametros.colaboradores_disponiveis[indiceAleatorio];\n        return {\n            colaborador_selecionado: colaboradorFallback.colaborador_user_id,\n            motivo_selecao: 'ultimo_disponivel',\n            estatisticas_antes: calcularEstatisticasDistribuicao(parametros.colaboradores_disponiveis)\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/roundRobin.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://tlbpsdgoklkekoxzmzlo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsYnBzZGdva2xrZWtveHptemxvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2OTI4ODYsImV4cCI6MjA2NDI2ODg4Nn0._0Jj8aLx_WTyMkFoiFviThur0EW5jC3hYVOoVrrntWA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase URL or Anon Key');\n}\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLGVBQWUsSUFBTVIsa0VBQW1CQSxDQUFDQyxhQUFhSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxzcmNcXHV0aWxzXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMO1xyXG5jb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWTtcclxuXHJcbmlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlQW5vbktleSkge1xyXG4gIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBTdXBhYmFzZSBVUkwgb3IgQW5vbiBLZXknKTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9ICgpID0+IGNyZWF0ZUJyb3dzZXJDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSk7XHJcblxyXG4vLyBUaXBvcyBwYXJhIGF1dGVudGljYcOnw6NvXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlclByb2ZpbGUge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBuYW1lPzogc3RyaW5nO1xyXG4gIHBob25lPzogc3RyaW5nO1xyXG4gIHJvbGU6ICdBZG1pbmlzdHJhZG9yJyB8ICdQcm9wcmlldGFyaW8nIHwgJ0NvbGFib3JhZG9yJyB8ICdVc3VhcmlvJztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIHBhZ2FtZW50b19jb25maXJtYWRvPzogYm9vbGVhbjtcclxuICBvbmJvYXJkaW5nX3BlbmRlbnRlPzogYm9vbGVhbjtcclxuICBwbGFub19zZWxlY2lvbmFkbz86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBdXRoU3RhdGUge1xyXG4gIHVzZXI6IFVzZXJQcm9maWxlIHwgbnVsbDtcclxuICBzZXNzaW9uOiBhbnkgfCBudWxsO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgaW5pdGlhbGl6ZWQ6IGJvb2xlYW47XHJcbn0iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRXJyb3IiLCJjcmVhdGVDbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/client.ts\n");

/***/ })

};
;