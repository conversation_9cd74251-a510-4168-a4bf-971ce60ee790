/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/agendamentos/route";
exports.ids = ["app/api/agendamentos/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagendamentos%2Froute&page=%2Fapi%2Fagendamentos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagendamentos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagendamentos%2Froute&page=%2Fapi%2Fagendamentos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagendamentos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projetos_1_geremias_servicetech_src_app_api_agendamentos_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/agendamentos/route.ts */ \"(rsc)/./src/app/api/agendamentos/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/agendamentos/route\",\n        pathname: \"/api/agendamentos\",\n        filename: \"route\",\n        bundlePath: \"app/api/agendamentos/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\api\\\\agendamentos\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projetos_1_geremias_servicetech_src_app_api_agendamentos_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagendamentos%2Froute&page=%2Fapi%2Fagendamentos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagendamentos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/agendamentos/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/agendamentos/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n\n\n// GET - Buscar agendamentos\nasync function GET(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { searchParams } = new URL(request.url);\n        // Verificar autenticação\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Usuário não autenticado'\n            }, {\n                status: 401\n            });\n        }\n        // Extrair filtros da query\n        const empresa_id = searchParams.get('empresa_id');\n        const status_agendamento = searchParams.get('status_agendamento');\n        const data_inicio = searchParams.get('data_inicio');\n        const data_fim = searchParams.get('data_fim');\n        // Construir query base\n        let query = supabase.from('agendamentos').select(`\n        agendamento_id,\n        cliente_user_id,\n        empresa_id,\n        colaborador_user_id,\n        servico_id,\n        data_hora_inicio,\n        data_hora_fim,\n        observacoes_cliente,\n        status_agendamento,\n        forma_pagamento,\n        status_pagamento,\n        valor_total,\n        valor_desconto,\n        codigo_confirmacao,\n        prazo_confirmacao,\n        created_at,\n        updated_at,\n        empresas!inner (\n          nome_empresa,\n          endereco,\n          telefone\n        ),\n        servicos!inner (\n          nome_servico,\n          descricao,\n          duracao_minutos,\n          preco,\n          categoria\n        ),\n        colaboradores:auth.users!colaborador_user_id (\n          id,\n          user_metadata\n        ),\n        clientes:auth.users!cliente_user_id (\n          id,\n          user_metadata\n        )\n      `);\n        // Aplicar filtros baseados no papel do usuário\n        const userRole = user.user_metadata?.role;\n        if (userRole === 'Usuario') {\n            // Cliente só vê seus próprios agendamentos\n            query = query.eq('cliente_user_id', user.id);\n        } else if (userRole === 'Colaborador') {\n            // Colaborador vê agendamentos onde ele é o prestador\n            query = query.eq('colaborador_user_id', user.id);\n        } else if (userRole === 'Proprietario') {\n            // Proprietário vê agendamentos da sua empresa\n            if (!empresa_id) {\n                // Buscar empresa do proprietário\n                const { data: empresa } = await supabase.from('empresas').select('empresa_id').eq('proprietario_user_id', user.id).single();\n                if (empresa) {\n                    query = query.eq('empresa_id', empresa.empresa_id);\n                } else {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'Empresa não encontrada'\n                    }, {\n                        status: 404\n                    });\n                }\n            } else {\n                query = query.eq('empresa_id', empresa_id);\n            }\n        } else if (userRole !== 'Administrador') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Acesso negado'\n            }, {\n                status: 403\n            });\n        }\n        // Aplicar filtros adicionais\n        if (empresa_id && userRole === 'Administrador') {\n            query = query.eq('empresa_id', empresa_id);\n        }\n        if (status_agendamento) {\n            query = query.eq('status_agendamento', status_agendamento);\n        }\n        if (data_inicio) {\n            query = query.gte('data_hora_inicio', data_inicio);\n        }\n        if (data_fim) {\n            query = query.lte('data_hora_inicio', data_fim + ' 23:59:59');\n        }\n        // Ordenar por data\n        query = query.order('data_hora_inicio', {\n            ascending: true\n        });\n        const { data: agendamentos, error: agendamentosError } = await query;\n        if (agendamentosError) {\n            console.error('Erro ao buscar agendamentos:', agendamentosError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar agendamentos'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: agendamentos || []\n        });\n    } catch (error) {\n        console.error('Erro geral na API de agendamentos:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Criar novo agendamento\nasync function POST(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Verificar autenticação\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Usuário não autenticado'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { empresa_id, colaborador_user_id, servicos_ids, data_hora_inicio, observacoes_cliente, forma_pagamento, combo_id, valor_desconto } = body;\n        // Validar dados obrigatórios\n        if (!empresa_id || !servicos_ids || servicos_ids.length === 0 || !data_hora_inicio || !forma_pagamento) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Dados obrigatórios: empresa_id, servicos_ids (array), data_hora_inicio, forma_pagamento'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar se a empresa existe e está ativa\n        const { data: empresa, error: empresaError } = await supabase.from('empresas').select('empresa_id, nome_empresa, status').eq('empresa_id', empresa_id).eq('status', 'ativo').single();\n        if (empresaError || !empresa) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Empresa não encontrada ou inativa'\n            }, {\n                status: 404\n            });\n        }\n        // Verificar se os serviços existem e estão ativos\n        const { data: servicos, error: servicosError } = await supabase.from('servicos').select('servico_id, nome_servico, duracao_minutos, preco, ativo').in('servico_id', servicos_ids).eq('empresa_id', empresa_id).eq('ativo', true);\n        if (servicosError || !servicos || servicos.length !== servicos_ids.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Um ou mais serviços não foram encontrados ou estão inativos'\n            }, {\n                status: 404\n            });\n        }\n        // Calcular duração total e valor total\n        const duracaoTotalMinutos = servicos.reduce((total, servico)=>total + servico.duracao_minutos, 0);\n        const valorOriginal = servicos.reduce((total, servico)=>total + servico.preco, 0);\n        const valorFinal = valorOriginal - (valor_desconto || 0);\n        // Calcular data/hora de fim baseado na duração total dos serviços\n        const dataHoraInicio = new Date(data_hora_inicio);\n        const dataHoraFim = new Date(dataHoraInicio.getTime() + duracaoTotalMinutos * 60000);\n        // Se colaborador não foi especificado, usar round-robin\n        let colaboradorSelecionado = colaborador_user_id;\n        if (!colaboradorSelecionado) {\n            const { data: colaboradoresDisponiveis } = await supabase.from('colaboradores_empresa').select(`\n          colaborador_user_id,\n          auth_users:colaborador_user_id (\n            raw_user_meta_data\n          ),\n          colaborador_servicos!inner (\n            servico_id,\n            ativo\n          )\n        `).eq('empresa_id', empresa_id).eq('ativo', true).eq('ativo_como_prestador', true).in('colaborador_servicos.servico_id', servicos_ids).eq('colaborador_servicos.ativo', true);\n            if (colaboradoresDisponiveis && colaboradoresDisponiveis.length > 0) {\n                // Implementação de round-robin inteligente\n                try {\n                    const { selecionarColaboradorRoundRobin } = await __webpack_require__.e(/*! import() */ \"_rsc_src_utils_roundRobin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/roundRobin */ \"(rsc)/./src/utils/roundRobin.ts\"));\n                    const colaboradoresParaRoundRobin = colaboradoresDisponiveis.map((c)=>{\n                        const authUser = Array.isArray(c.auth_users) ? c.auth_users[0] : c.auth_users;\n                        return {\n                            colaborador_user_id: c.colaborador_user_id,\n                            name: authUser?.raw_user_meta_data?.name || 'Colaborador',\n                            email: authUser?.raw_user_meta_data?.email || '',\n                            total_agendamentos: 0 // Será calculado pela função\n                        };\n                    });\n                    const resultadoRoundRobin = await selecionarColaboradorRoundRobin({\n                        empresa_id,\n                        servico_id: servicos_ids[0],\n                        colaboradores_disponiveis: colaboradoresParaRoundRobin,\n                        data_hora_inicio: dataHoraInicio.toISOString(),\n                        data_hora_fim: dataHoraFim.toISOString()\n                    });\n                    colaboradorSelecionado = resultadoRoundRobin.colaborador_selecionado;\n                    console.log('✅ Round-Robin aplicado:', {\n                        colaborador_selecionado: colaboradorSelecionado,\n                        motivo: resultadoRoundRobin.motivo_selecao,\n                        total_colaboradores: colaboradoresDisponiveis.length\n                    });\n                } catch (error) {\n                    console.error('❌ Erro no round-robin, usando fallback aleatório:', error);\n                    // Fallback para seleção aleatória em caso de erro\n                    const indiceAleatorio = Math.floor(Math.random() * colaboradoresDisponiveis.length);\n                    colaboradorSelecionado = colaboradoresDisponiveis[indiceAleatorio].colaborador_user_id;\n                }\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Nenhum colaborador disponível para este serviço'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Verificar se o colaborador pode realizar todos os serviços\n        const { data: colaboradorServicos } = await supabase.from('colaborador_servicos').select('col_serv_id, servico_id').eq('colaborador_user_id', colaboradorSelecionado).in('servico_id', servicos_ids).eq('ativo', true);\n        if (!colaboradorServicos || colaboradorServicos.length !== servicos_ids.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Colaborador não pode realizar todos os serviços selecionados'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar conflitos de horário\n        const { data: conflitos } = await supabase.from('agendamentos').select('agendamento_id').eq('colaborador_user_id', colaboradorSelecionado).in('status_agendamento', [\n            'Pendente',\n            'Confirmado'\n        ]).or(`and(data_hora_inicio.lt.${dataHoraFim.toISOString()},data_hora_fim.gt.${dataHoraInicio.toISOString()})`);\n        if (conflitos && conflitos.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Horário não disponível - conflito com outro agendamento'\n            }, {\n                status: 409\n            });\n        }\n        // Calcular prazo de confirmação (24 horas)\n        const prazoConfirmacao = new Date(Date.now() + 24 * 60 * 60 * 1000);\n        // Criar agendamento principal (usando o primeiro serviço como referência)\n        const { data: novoAgendamento, error: criarError } = await supabase.from('agendamentos').insert([\n            {\n                cliente_user_id: user.id,\n                empresa_id: empresa_id,\n                colaborador_user_id: colaboradorSelecionado,\n                servico_id: servicos_ids[0],\n                data_hora_inicio: dataHoraInicio.toISOString(),\n                data_hora_fim: dataHoraFim.toISOString(),\n                observacoes_cliente: observacoes_cliente || '',\n                status_agendamento: 'Pendente',\n                forma_pagamento: forma_pagamento,\n                status_pagamento: forma_pagamento === 'Online' ? 'Pendente' : 'Pendente',\n                valor_total: valorFinal,\n                valor_desconto: valor_desconto || 0,\n                prazo_confirmacao: prazoConfirmacao.toISOString()\n            }\n        ]).select().single();\n        if (criarError) {\n            console.error('Erro ao criar agendamento:', criarError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao criar agendamento'\n            }, {\n                status: 500\n            });\n        }\n        // Criar registros para serviços adicionais (se houver mais de um)\n        if (servicos_ids.length > 1) {\n            const servicosAdicionais = servicos_ids.slice(1).map((servico_id, index)=>({\n                    agendamento_id: novoAgendamento.agendamento_id,\n                    servico_id: servico_id,\n                    ordem_execucao: index + 2 // Começar do 2, pois o primeiro já está no agendamento principal\n                }));\n            const { error: servicosError } = await supabase.from('agendamento_servicos').insert(servicosAdicionais);\n            if (servicosError) {\n                console.error('Erro ao criar serviços adicionais:', servicosError);\n            // Não falhar o agendamento por isso, apenas logar\n            }\n        }\n        // Registrar combo aplicado (se houver)\n        if (combo_id && valor_desconto && valor_desconto > 0) {\n            const { error: comboError } = await supabase.from('agendamento_combos').insert([\n                {\n                    agendamento_id: novoAgendamento.agendamento_id,\n                    combo_id: combo_id,\n                    valor_desconto: valor_desconto,\n                    aplicado_em: new Date().toISOString()\n                }\n            ]);\n            if (comboError) {\n                console.error('Erro ao registrar combo aplicado:', comboError);\n            // Não falhar o agendamento por isso, apenas logar\n            }\n        }\n        console.log('✅ Agendamento criado com sucesso:', {\n            agendamento_id: novoAgendamento.agendamento_id,\n            servicos_count: servicos_ids.length,\n            valor_original: valorOriginal,\n            valor_desconto: valor_desconto || 0,\n            valor_final: valorFinal,\n            combo_aplicado: !!combo_id\n        });\n        // Enviar notificações por email (não bloquear a resposta)\n        try {\n            const { notificarNovoAgendamento } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/date-fns\"), __webpack_require__.e(\"vendor-chunks/twilio\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/xmlbuilder\"), __webpack_require__.e(\"vendor-chunks/jsonwebtoken\"), __webpack_require__.e(\"vendor-chunks/asynckit\"), __webpack_require__.e(\"vendor-chunks/math-intrinsics\"), __webpack_require__.e(\"vendor-chunks/es-errors\"), __webpack_require__.e(\"vendor-chunks/qs\"), __webpack_require__.e(\"vendor-chunks/call-bind-apply-helpers\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/get-proto\"), __webpack_require__.e(\"vendor-chunks/axios\"), __webpack_require__.e(\"vendor-chunks/scmp\"), __webpack_require__.e(\"vendor-chunks/object-inspect\"), __webpack_require__.e(\"vendor-chunks/mime-db\"), __webpack_require__.e(\"vendor-chunks/has-symbols\"), __webpack_require__.e(\"vendor-chunks/gopd\"), __webpack_require__.e(\"vendor-chunks/function-bind\"), __webpack_require__.e(\"vendor-chunks/follow-redirects\"), __webpack_require__.e(\"vendor-chunks/ecdsa-sig-formatter\"), __webpack_require__.e(\"vendor-chunks/dayjs\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/resend\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/side-channel\"), __webpack_require__.e(\"vendor-chunks/side-channel-weakmap\"), __webpack_require__.e(\"vendor-chunks/side-channel-map\"), __webpack_require__.e(\"vendor-chunks/side-channel-list\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/proxy-from-env\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/mime-types\"), __webpack_require__.e(\"vendor-chunks/lodash.once\"), __webpack_require__.e(\"vendor-chunks/lodash.isstring\"), __webpack_require__.e(\"vendor-chunks/lodash.isplainobject\"), __webpack_require__.e(\"vendor-chunks/lodash.isnumber\"), __webpack_require__.e(\"vendor-chunks/lodash.isinteger\"), __webpack_require__.e(\"vendor-chunks/lodash.isboolean\"), __webpack_require__.e(\"vendor-chunks/lodash.includes\"), __webpack_require__.e(\"vendor-chunks/hasown\"), __webpack_require__.e(\"vendor-chunks/has-tostringtag\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/get-intrinsic\"), __webpack_require__.e(\"vendor-chunks/es-set-tostringtag\"), __webpack_require__.e(\"vendor-chunks/es-object-atoms\"), __webpack_require__.e(\"vendor-chunks/es-define-property\"), __webpack_require__.e(\"vendor-chunks/dunder-proto\"), __webpack_require__.e(\"vendor-chunks/delayed-stream\"), __webpack_require__.e(\"vendor-chunks/combined-stream\"), __webpack_require__.e(\"vendor-chunks/call-bound\"), __webpack_require__.e(\"vendor-chunks/buffer-equal-constant-time\"), __webpack_require__.e(\"_rsc_src_utils_notificationHelpers_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/utils/notificationHelpers */ \"(rsc)/./src/utils/notificationHelpers.ts\"));\n            // Executar em background sem aguardar\n            notificarNovoAgendamento(novoAgendamento.agendamento_id).catch((error)=>{\n                console.error('❌ Erro ao enviar notificações de novo agendamento:', error);\n            });\n        } catch (error) {\n            console.error('❌ Erro ao importar helper de notificações:', error);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: novoAgendamento,\n            message: 'Agendamento criado com sucesso'\n        });\n    } catch (error) {\n        console.error('Erro geral ao criar agendamento:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/agendamentos/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Cliente administrativo para operações que requerem service role\nfunction createAdminClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // No-op for admin client\n            }\n        },\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fagendamentos%2Froute&page=%2Fapi%2Fagendamentos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagendamentos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();