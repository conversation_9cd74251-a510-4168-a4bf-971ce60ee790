/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/empresas/[id]/route";
exports.ids = ["app/api/empresas/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projetos_1_geremias_servicetech_src_app_api_empresas_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/empresas/[id]/route.ts */ \"(rsc)/./src/app/api/empresas/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/empresas/[id]/route\",\n        pathname: \"/api/empresas/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/empresas/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\api\\\\empresas\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projetos_1_geremias_servicetech_src_app_api_empresas_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/empresas/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/empresas/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n\n\n// GET - Buscar dados públicos da empresa\nasync function GET(request, { params }) {\n    try {\n        const supabase = (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createAdminClient)();\n        const resolvedParams = await params;\n        const identificador = resolvedParams.id;\n        console.log('🔍 API: Recebida requisição para empresa:', identificador);\n        console.log('🔍 API: URL da requisição:', request.url);\n        console.log('🔍 API: Headers da requisição:', Object.fromEntries(request.headers.entries()));\n        // Validar se o identificador foi fornecido\n        if (!identificador) {\n            console.log('❌ API: Identificador não fornecido');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Identificador da empresa é obrigatório'\n            }, {\n                status: 400\n            });\n        }\n        // Determinar se é ID numérico ou slug\n        const isNumericId = !isNaN(Number(identificador));\n        console.log('🔍 API: Tipo de identificador - isNumericId:', isNumericId);\n        // Construir query baseada no tipo de identificador\n        let query = supabase.from('empresas').select(`\n        empresa_id,\n        nome_empresa,\n        cnpj,\n        telefone,\n        endereco,\n        numero,\n        complemento,\n        bairro,\n        cidade,\n        estado,\n        cep,\n        descricao,\n        logo_url,\n        fotos_portfolio_urls,\n        horario_funcionamento,\n        segmento,\n        slug,\n        status\n      `).eq('status', 'ativo');\n        // Aplicar filtro baseado no tipo de identificador\n        if (isNumericId) {\n            console.log('🔍 API: Buscando por ID numérico:', Number(identificador));\n            query = query.eq('empresa_id', Number(identificador));\n        } else {\n            console.log('🔍 API: Buscando por slug:', identificador);\n            query = query.eq('slug', identificador);\n        }\n        // Buscar dados públicos da empresa\n        console.log('🔍 API: Executando query no Supabase...');\n        const { data: empresa, error: empresaError } = await query.single();\n        console.log('📊 API: Resultado da query empresa:');\n        console.log('   - empresa:', empresa ? 'encontrada' : 'null');\n        console.log('   - empresaError:', empresaError);\n        if (empresaError) {\n            console.error('❌ API: Erro ao buscar empresa:', empresaError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Empresa não encontrada'\n            }, {\n                status: 404\n            });\n        }\n        if (!empresa) {\n            console.error('❌ API: Empresa não encontrada (data é null)');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Empresa não encontrada ou inativa'\n            }, {\n                status: 404\n            });\n        }\n        console.log('✅ API: Empresa encontrada:', empresa.nome_empresa);\n        // Buscar serviços ativos da empresa\n        const { data: servicos, error: servicosError } = await supabase.from('servicos').select(`\n        servico_id,\n        nome_servico,\n        descricao,\n        duracao_minutos,\n        preco,\n        categoria\n      `).eq('empresa_id', empresa.empresa_id).eq('ativo', true).order('categoria', {\n            ascending: true\n        }).order('nome_servico', {\n            ascending: true\n        });\n        if (servicosError) {\n            console.error('Erro ao buscar serviços:', servicosError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar serviços da empresa'\n            }, {\n                status: 500\n            });\n        }\n        // Buscar colaboradores ativos da empresa (dados públicos)\n        const { data: colaboradores, error: colaboradoresError } = await supabase.from('colaboradores_empresa').select(`\n        colaborador_user_id,\n        ativo_como_prestador,\n        auth_users:colaborador_user_id (\n          raw_user_meta_data\n        )\n      `).eq('empresa_id', empresa.empresa_id).eq('ativo', true).eq('ativo_como_prestador', true);\n        if (colaboradoresError) {\n            console.error('Erro ao buscar colaboradores:', colaboradoresError);\n        // Não retorna erro, apenas colaboradores vazios\n        }\n        // Processar dados dos colaboradores\n        const colaboradoresProcessados = colaboradores?.map((col)=>{\n            const authUser = Array.isArray(col.auth_users) ? col.auth_users[0] : col.auth_users;\n            return {\n                colaborador_user_id: col.colaborador_user_id,\n                name: authUser?.raw_user_meta_data?.name || 'Colaborador',\n                ativo_como_prestador: col.ativo_como_prestador\n            };\n        }) || [];\n        // Agrupar serviços por categoria\n        const servicosPorCategoria = servicos?.reduce((acc, servico)=>{\n            const categoria = servico.categoria || 'Outros';\n            if (!acc[categoria]) {\n                acc[categoria] = [];\n            }\n            acc[categoria].push(servico);\n            return acc;\n        }, {}) || {};\n        // Montar resposta com dados públicos no formato esperado pela página\n        const dadosPublicos = {\n            empresa: {\n                empresa_id: empresa.empresa_id,\n                nome_empresa: empresa.nome_empresa,\n                telefone: empresa.telefone,\n                endereco: empresa.endereco,\n                numero: empresa.numero,\n                complemento: empresa.complemento,\n                bairro: empresa.bairro,\n                cidade: empresa.cidade,\n                estado: empresa.estado,\n                cep: empresa.cep,\n                descricao: empresa.descricao,\n                logo_url: empresa.logo_url,\n                imagem_capa_url: null,\n                fotos_portfolio_urls: empresa.fotos_portfolio_urls,\n                horario_funcionamento: empresa.horario_funcionamento,\n                segmento: empresa.segmento,\n                slug: empresa.slug,\n                // Campos do Stripe Connect (temporariamente removidos até migração)\n                pagamentos_online_habilitados: false,\n                stripe_charges_enabled: false\n            },\n            servicos: servicos || [],\n            servicos_por_categoria: servicosPorCategoria,\n            colaboradores: colaboradoresProcessados,\n            estatisticas: {\n                total_servicos: servicos?.length || 0,\n                total_colaboradores: colaboradoresProcessados.length,\n                categorias_servicos: Object.keys(servicosPorCategoria).length\n            }\n        };\n        console.log('✅ API: Retornando dados com sucesso');\n        console.log('📊 API: Dados incluem:', Object.keys(dadosPublicos).join(', '));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: dadosPublicos\n        });\n    } catch (error) {\n        console.error('Erro geral na API de empresa:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/empresas/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Cliente administrativo para operações que requerem service role\nfunction createAdminClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // No-op for admin client\n            }\n        },\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fempresas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();