"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agent-base";
exports.ids = ["vendor-chunks/agent-base"];
exports.modules = {

/***/ "(rsc)/./node_modules/agent-base/dist/src/index.js":
/*!***************************************************!*\
  !*** ./node_modules/agent-base/dist/src/index.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst promisify_1 = __importDefault(__webpack_require__(/*! ./promisify */ \"(rsc)/./node_modules/agent-base/dist/src/promisify.js\"));\nconst debug = debug_1.default('agent-base');\nfunction isAgent(v) {\n    return Boolean(v) && typeof v.addRequest === 'function';\n}\nfunction isSecureEndpoint() {\n    const { stack } = new Error();\n    if (typeof stack !== 'string')\n        return false;\n    return stack.split('\\n').some(l => l.indexOf('(https.js:') !== -1 || l.indexOf('node:https:') !== -1);\n}\nfunction createAgent(callback, opts) {\n    return new createAgent.Agent(callback, opts);\n}\n(function (createAgent) {\n    /**\n     * Base `http.Agent` implementation.\n     * No pooling/keep-alive is implemented by default.\n     *\n     * @param {Function} callback\n     * @api public\n     */\n    class Agent extends events_1.EventEmitter {\n        constructor(callback, _opts) {\n            super();\n            let opts = _opts;\n            if (typeof callback === 'function') {\n                this.callback = callback;\n            }\n            else if (callback) {\n                opts = callback;\n            }\n            // Timeout for the socket to be returned from the callback\n            this.timeout = null;\n            if (opts && typeof opts.timeout === 'number') {\n                this.timeout = opts.timeout;\n            }\n            // These aren't actually used by `agent-base`, but are required\n            // for the TypeScript definition files in `@types/node` :/\n            this.maxFreeSockets = 1;\n            this.maxSockets = 1;\n            this.maxTotalSockets = Infinity;\n            this.sockets = {};\n            this.freeSockets = {};\n            this.requests = {};\n            this.options = {};\n        }\n        get defaultPort() {\n            if (typeof this.explicitDefaultPort === 'number') {\n                return this.explicitDefaultPort;\n            }\n            return isSecureEndpoint() ? 443 : 80;\n        }\n        set defaultPort(v) {\n            this.explicitDefaultPort = v;\n        }\n        get protocol() {\n            if (typeof this.explicitProtocol === 'string') {\n                return this.explicitProtocol;\n            }\n            return isSecureEndpoint() ? 'https:' : 'http:';\n        }\n        set protocol(v) {\n            this.explicitProtocol = v;\n        }\n        callback(req, opts, fn) {\n            throw new Error('\"agent-base\" has no default implementation, you must subclass and override `callback()`');\n        }\n        /**\n         * Called by node-core's \"_http_client.js\" module when creating\n         * a new HTTP request with this Agent instance.\n         *\n         * @api public\n         */\n        addRequest(req, _opts) {\n            const opts = Object.assign({}, _opts);\n            if (typeof opts.secureEndpoint !== 'boolean') {\n                opts.secureEndpoint = isSecureEndpoint();\n            }\n            if (opts.host == null) {\n                opts.host = 'localhost';\n            }\n            if (opts.port == null) {\n                opts.port = opts.secureEndpoint ? 443 : 80;\n            }\n            if (opts.protocol == null) {\n                opts.protocol = opts.secureEndpoint ? 'https:' : 'http:';\n            }\n            if (opts.host && opts.path) {\n                // If both a `host` and `path` are specified then it's most\n                // likely the result of a `url.parse()` call... we need to\n                // remove the `path` portion so that `net.connect()` doesn't\n                // attempt to open that as a unix socket file.\n                delete opts.path;\n            }\n            delete opts.agent;\n            delete opts.hostname;\n            delete opts._defaultAgent;\n            delete opts.defaultPort;\n            delete opts.createConnection;\n            // Hint to use \"Connection: close\"\n            // XXX: non-documented `http` module API :(\n            req._last = true;\n            req.shouldKeepAlive = false;\n            let timedOut = false;\n            let timeoutId = null;\n            const timeoutMs = opts.timeout || this.timeout;\n            const onerror = (err) => {\n                if (req._hadError)\n                    return;\n                req.emit('error', err);\n                // For Safety. Some additional errors might fire later on\n                // and we need to make sure we don't double-fire the error event.\n                req._hadError = true;\n            };\n            const ontimeout = () => {\n                timeoutId = null;\n                timedOut = true;\n                const err = new Error(`A \"socket\" was not created for HTTP request before ${timeoutMs}ms`);\n                err.code = 'ETIMEOUT';\n                onerror(err);\n            };\n            const callbackError = (err) => {\n                if (timedOut)\n                    return;\n                if (timeoutId !== null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                onerror(err);\n            };\n            const onsocket = (socket) => {\n                if (timedOut)\n                    return;\n                if (timeoutId != null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                if (isAgent(socket)) {\n                    // `socket` is actually an `http.Agent` instance, so\n                    // relinquish responsibility for this `req` to the Agent\n                    // from here on\n                    debug('Callback returned another Agent instance %o', socket.constructor.name);\n                    socket.addRequest(req, opts);\n                    return;\n                }\n                if (socket) {\n                    socket.once('free', () => {\n                        this.freeSocket(socket, opts);\n                    });\n                    req.onSocket(socket);\n                    return;\n                }\n                const err = new Error(`no Duplex stream was returned to agent-base for \\`${req.method} ${req.path}\\``);\n                onerror(err);\n            };\n            if (typeof this.callback !== 'function') {\n                onerror(new Error('`callback` is not defined'));\n                return;\n            }\n            if (!this.promisifiedCallback) {\n                if (this.callback.length >= 3) {\n                    debug('Converting legacy callback function to promise');\n                    this.promisifiedCallback = promisify_1.default(this.callback);\n                }\n                else {\n                    this.promisifiedCallback = this.callback;\n                }\n            }\n            if (typeof timeoutMs === 'number' && timeoutMs > 0) {\n                timeoutId = setTimeout(ontimeout, timeoutMs);\n            }\n            if ('port' in opts && typeof opts.port !== 'number') {\n                opts.port = Number(opts.port);\n            }\n            try {\n                debug('Resolving socket for %o request: %o', opts.protocol, `${req.method} ${req.path}`);\n                Promise.resolve(this.promisifiedCallback(req, opts)).then(onsocket, callbackError);\n            }\n            catch (err) {\n                Promise.reject(err).catch(callbackError);\n            }\n        }\n        freeSocket(socket, opts) {\n            debug('Freeing socket %o %o', socket.constructor.name, opts);\n            socket.destroy();\n        }\n        destroy() {\n            debug('Destroying agent %o', this.constructor.name);\n        }\n    }\n    createAgent.Agent = Agent;\n    // So that `instanceof` works correctly\n    createAgent.prototype = createAgent.Agent.prototype;\n})(createAgent || (createAgent = {}));\nmodule.exports = createAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agent-base/dist/src/promisify.js":
/*!*******************************************************!*\
  !*** ./node_modules/agent-base/dist/src/promisify.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction promisify(fn) {\n    return function (req, opts) {\n        return new Promise((resolve, reject) => {\n            fn.call(this, req, opts, (err, rtn) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(rtn);\n                }\n            });\n        });\n    };\n}\nexports[\"default\"] = promisify;\n//# sourceMappingURL=promisify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnQtYmFzZS9kaXN0L3NyYy9wcm9taXNpZnkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSxrQkFBZTtBQUNmIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFxhZ2VudC1iYXNlXFxkaXN0XFxzcmNcXHByb21pc2lmeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmZ1bmN0aW9uIHByb21pc2lmeShmbikge1xuICAgIHJldHVybiBmdW5jdGlvbiAocmVxLCBvcHRzKSB7XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICBmbi5jYWxsKHRoaXMsIHJlcSwgb3B0cywgKGVyciwgcnRuKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QoZXJyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmUocnRuKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfTtcbn1cbmV4cG9ydHMuZGVmYXVsdCA9IHByb21pc2lmeTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByb21pc2lmeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/src/promisify.js\n");

/***/ })

};
;