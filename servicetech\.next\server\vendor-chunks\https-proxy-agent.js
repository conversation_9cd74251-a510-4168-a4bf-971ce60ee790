"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/https-proxy-agent";
exports.ids = ["vendor-chunks/https-proxy-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/agent.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/agent.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst net_1 = __importDefault(__webpack_require__(/*! net */ \"net\"));\nconst tls_1 = __importDefault(__webpack_require__(/*! tls */ \"tls\"));\nconst url_1 = __importDefault(__webpack_require__(/*! url */ \"url\"));\nconst assert_1 = __importDefault(__webpack_require__(/*! assert */ \"assert\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(rsc)/./node_modules/agent-base/dist/src/index.js\");\nconst parse_proxy_response_1 = __importDefault(__webpack_require__(/*! ./parse-proxy-response */ \"(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\"));\nconst debug = debug_1.default('https-proxy-agent:agent');\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n *\n * @api public\n */\nclass HttpsProxyAgent extends agent_base_1.Agent {\n    constructor(_opts) {\n        let opts;\n        if (typeof _opts === 'string') {\n            opts = url_1.default.parse(_opts);\n        }\n        else {\n            opts = _opts;\n        }\n        if (!opts) {\n            throw new Error('an HTTP(S) proxy server `host` and `port` must be specified!');\n        }\n        debug('creating new HttpsProxyAgent instance: %o', opts);\n        super(opts);\n        const proxy = Object.assign({}, opts);\n        // If `true`, then connect to the proxy server over TLS.\n        // Defaults to `false`.\n        this.secureProxy = opts.secureProxy || isHTTPS(proxy.protocol);\n        // Prefer `hostname` over `host`, and set the `port` if needed.\n        proxy.host = proxy.hostname || proxy.host;\n        if (typeof proxy.port === 'string') {\n            proxy.port = parseInt(proxy.port, 10);\n        }\n        if (!proxy.port && proxy.host) {\n            proxy.port = this.secureProxy ? 443 : 80;\n        }\n        // ALPN is supported by Node.js >= v5.\n        // attempt to negotiate http/1.1 for proxy servers that support http/2\n        if (this.secureProxy && !('ALPNProtocols' in proxy)) {\n            proxy.ALPNProtocols = ['http 1.1'];\n        }\n        if (proxy.host && proxy.path) {\n            // If both a `host` and `path` are specified then it's most likely\n            // the result of a `url.parse()` call... we need to remove the\n            // `path` portion so that `net.connect()` doesn't attempt to open\n            // that as a Unix socket file.\n            delete proxy.path;\n            delete proxy.pathname;\n        }\n        this.proxy = proxy;\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     *\n     * @api protected\n     */\n    callback(req, opts) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const { proxy, secureProxy } = this;\n            // Create a socket connection to the proxy server.\n            let socket;\n            if (secureProxy) {\n                debug('Creating `tls.Socket`: %o', proxy);\n                socket = tls_1.default.connect(proxy);\n            }\n            else {\n                debug('Creating `net.Socket`: %o', proxy);\n                socket = net_1.default.connect(proxy);\n            }\n            const headers = Object.assign({}, proxy.headers);\n            const hostname = `${opts.host}:${opts.port}`;\n            let payload = `CONNECT ${hostname} HTTP/1.1\\r\\n`;\n            // Inject the `Proxy-Authorization` header if necessary.\n            if (proxy.auth) {\n                headers['Proxy-Authorization'] = `Basic ${Buffer.from(proxy.auth).toString('base64')}`;\n            }\n            // The `Host` header should only include the port\n            // number when it is not the default port.\n            let { host, port, secureEndpoint } = opts;\n            if (!isDefaultPort(port, secureEndpoint)) {\n                host += `:${port}`;\n            }\n            headers.Host = host;\n            headers.Connection = 'close';\n            for (const name of Object.keys(headers)) {\n                payload += `${name}: ${headers[name]}\\r\\n`;\n            }\n            const proxyResponsePromise = parse_proxy_response_1.default(socket);\n            socket.write(`${payload}\\r\\n`);\n            const { statusCode, buffered } = yield proxyResponsePromise;\n            if (statusCode === 200) {\n                req.once('socket', resume);\n                if (opts.secureEndpoint) {\n                    // The proxy is connecting to a TLS server, so upgrade\n                    // this socket connection to a TLS connection.\n                    debug('Upgrading socket connection to TLS');\n                    const servername = opts.servername || opts.host;\n                    return tls_1.default.connect(Object.assign(Object.assign({}, omit(opts, 'host', 'hostname', 'path', 'port')), { socket,\n                        servername }));\n                }\n                return socket;\n            }\n            // Some other status code that's not 200... need to re-play the HTTP\n            // header \"data\" events onto the socket once the HTTP machinery is\n            // attached so that the node core `http` can parse and handle the\n            // error status code.\n            // Close the original socket, and a new \"fake\" socket is returned\n            // instead, so that the proxy doesn't get the HTTP request\n            // written to it (which may contain `Authorization` headers or other\n            // sensitive data).\n            //\n            // See: https://hackerone.com/reports/541502\n            socket.destroy();\n            const fakeSocket = new net_1.default.Socket({ writable: false });\n            fakeSocket.readable = true;\n            // Need to wait for the \"socket\" event to re-play the \"data\" events.\n            req.once('socket', (s) => {\n                debug('replaying proxy buffer for failed request');\n                assert_1.default(s.listenerCount('data') > 0);\n                // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n                // this point the HTTP module machinery has been hooked up for\n                // the user.\n                s.push(buffered);\n                s.push(null);\n            });\n            return fakeSocket;\n        });\n    }\n}\nexports[\"default\"] = HttpsProxyAgent;\nfunction resume(socket) {\n    socket.resume();\n}\nfunction isDefaultPort(port, secure) {\n    return Boolean((!secure && port === 80) || (secure && port === 443));\n}\nfunction isHTTPS(protocol) {\n    return typeof protocol === 'string' ? /^https:?$/i.test(protocol) : false;\n}\nfunction omit(obj, ...keys) {\n    const ret = {};\n    let key;\n    for (key in obj) {\n        if (!keys.includes(key)) {\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=agent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/index.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst agent_1 = __importDefault(__webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/agent.js\"));\nfunction createHttpsProxyAgent(opts) {\n    return new agent_1.default(opts);\n}\n(function (createHttpsProxyAgent) {\n    createHttpsProxyAgent.HttpsProxyAgent = agent_1.default;\n    createHttpsProxyAgent.prototype = agent_1.default.prototype;\n})(createHttpsProxyAgent || (createHttpsProxyAgent = {}));\nmodule.exports = createHttpsProxyAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cHMtcHJveHktYWdlbnQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsZ0NBQWdDLG1CQUFPLENBQUMscUVBQVM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzREFBc0Q7QUFDdkQ7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcaHR0cHMtcHJveHktYWdlbnRcXGRpc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuY29uc3QgYWdlbnRfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi9hZ2VudFwiKSk7XG5mdW5jdGlvbiBjcmVhdGVIdHRwc1Byb3h5QWdlbnQob3B0cykge1xuICAgIHJldHVybiBuZXcgYWdlbnRfMS5kZWZhdWx0KG9wdHMpO1xufVxuKGZ1bmN0aW9uIChjcmVhdGVIdHRwc1Byb3h5QWdlbnQpIHtcbiAgICBjcmVhdGVIdHRwc1Byb3h5QWdlbnQuSHR0cHNQcm94eUFnZW50ID0gYWdlbnRfMS5kZWZhdWx0O1xuICAgIGNyZWF0ZUh0dHBzUHJveHlBZ2VudC5wcm90b3R5cGUgPSBhZ2VudF8xLmRlZmF1bHQucHJvdG90eXBlO1xufSkoY3JlYXRlSHR0cHNQcm94eUFnZW50IHx8IChjcmVhdGVIdHRwc1Byb3h5QWdlbnQgPSB7fSkpO1xubW9kdWxlLmV4cG9ydHMgPSBjcmVhdGVIdHRwc1Byb3h5QWdlbnQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js":
/*!*********************************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/parse-proxy-response.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst debug = debug_1.default('https-proxy-agent:parse-proxy-response');\nfunction parseProxyResponse(socket) {\n    return new Promise((resolve, reject) => {\n        // we need to buffer any HTTP traffic that happens with the proxy before we get\n        // the CONNECT response, so that if the response is anything other than an \"200\"\n        // response code, then we can re-play the \"data\" events on the socket once the\n        // HTTP parser is hooked up...\n        let buffersLength = 0;\n        const buffers = [];\n        function read() {\n            const b = socket.read();\n            if (b)\n                ondata(b);\n            else\n                socket.once('readable', read);\n        }\n        function cleanup() {\n            socket.removeListener('end', onend);\n            socket.removeListener('error', onerror);\n            socket.removeListener('close', onclose);\n            socket.removeListener('readable', read);\n        }\n        function onclose(err) {\n            debug('onclose had error %o', err);\n        }\n        function onend() {\n            debug('onend');\n        }\n        function onerror(err) {\n            cleanup();\n            debug('onerror %o', err);\n            reject(err);\n        }\n        function ondata(b) {\n            buffers.push(b);\n            buffersLength += b.length;\n            const buffered = Buffer.concat(buffers, buffersLength);\n            const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n            if (endOfHeaders === -1) {\n                // keep buffering\n                debug('have not received end of HTTP headers yet...');\n                read();\n                return;\n            }\n            const firstLine = buffered.toString('ascii', 0, buffered.indexOf('\\r\\n'));\n            const statusCode = +firstLine.split(' ')[1];\n            debug('got proxy server response: %o', firstLine);\n            resolve({\n                statusCode,\n                buffered\n            });\n        }\n        socket.on('error', onerror);\n        socket.on('close', onclose);\n        socket.on('end', onend);\n        read();\n    });\n}\nexports[\"default\"] = parseProxyResponse;\n//# sourceMappingURL=parse-proxy-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\n");

/***/ })

};
;