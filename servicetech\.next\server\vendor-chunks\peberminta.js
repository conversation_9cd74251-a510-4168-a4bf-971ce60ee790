"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/peberminta";
exports.ids = ["vendor-chunks/peberminta"];
exports.modules = {

/***/ "(rsc)/./node_modules/peberminta/lib/core.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/core.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ab: () => (/* binding */ ab),\n/* harmony export */   abc: () => (/* binding */ abc),\n/* harmony export */   action: () => (/* binding */ action),\n/* harmony export */   ahead: () => (/* binding */ ahead),\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   and: () => (/* binding */ all),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   chain: () => (/* binding */ chain),\n/* harmony export */   chainReduce: () => (/* binding */ chainReduce),\n/* harmony export */   choice: () => (/* binding */ choice),\n/* harmony export */   condition: () => (/* binding */ condition),\n/* harmony export */   decide: () => (/* binding */ decide),\n/* harmony export */   discard: () => (/* binding */ skip),\n/* harmony export */   eitherOr: () => (/* binding */ otherwise),\n/* harmony export */   emit: () => (/* binding */ emit),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   eof: () => (/* binding */ end),\n/* harmony export */   error: () => (/* binding */ error),\n/* harmony export */   fail: () => (/* binding */ fail),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   flatten1: () => (/* binding */ flatten1),\n/* harmony export */   left: () => (/* binding */ left),\n/* harmony export */   leftAssoc1: () => (/* binding */ leftAssoc1),\n/* harmony export */   leftAssoc2: () => (/* binding */ leftAssoc2),\n/* harmony export */   longest: () => (/* binding */ longest),\n/* harmony export */   lookAhead: () => (/* binding */ ahead),\n/* harmony export */   make: () => (/* binding */ make),\n/* harmony export */   many: () => (/* binding */ many),\n/* harmony export */   many1: () => (/* binding */ many1),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   map1: () => (/* binding */ map1),\n/* harmony export */   match: () => (/* binding */ match),\n/* harmony export */   middle: () => (/* binding */ middle),\n/* harmony export */   not: () => (/* binding */ not),\n/* harmony export */   of: () => (/* binding */ emit),\n/* harmony export */   option: () => (/* binding */ option),\n/* harmony export */   or: () => (/* binding */ choice),\n/* harmony export */   otherwise: () => (/* binding */ otherwise),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parserPosition: () => (/* binding */ parserPosition),\n/* harmony export */   peek: () => (/* binding */ peek),\n/* harmony export */   recursive: () => (/* binding */ recursive),\n/* harmony export */   reduceLeft: () => (/* binding */ reduceLeft),\n/* harmony export */   reduceRight: () => (/* binding */ reduceRight),\n/* harmony export */   remainingTokensNumber: () => (/* binding */ remainingTokensNumber),\n/* harmony export */   right: () => (/* binding */ right),\n/* harmony export */   rightAssoc1: () => (/* binding */ rightAssoc1),\n/* harmony export */   rightAssoc2: () => (/* binding */ rightAssoc2),\n/* harmony export */   satisfy: () => (/* binding */ satisfy),\n/* harmony export */   sepBy: () => (/* binding */ sepBy),\n/* harmony export */   sepBy1: () => (/* binding */ sepBy1),\n/* harmony export */   skip: () => (/* binding */ skip),\n/* harmony export */   some: () => (/* binding */ many1),\n/* harmony export */   start: () => (/* binding */ start),\n/* harmony export */   takeUntil: () => (/* binding */ takeUntil),\n/* harmony export */   takeUntilP: () => (/* binding */ takeUntilP),\n/* harmony export */   takeWhile: () => (/* binding */ takeWhile),\n/* harmony export */   takeWhileP: () => (/* binding */ takeWhileP),\n/* harmony export */   token: () => (/* binding */ token),\n/* harmony export */   tryParse: () => (/* binding */ tryParse)\n/* harmony export */ });\n/* harmony import */ var _util_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.mjs */ \"(rsc)/./node_modules/peberminta/lib/util.mjs\");\n\n\nfunction emit(value) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: value\n    });\n}\nfunction make(\nf) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: f(data, i)\n    });\n}\nfunction action(\nf) {\n    return (data, i) => {\n        f(data, i);\n        return {\n            matched: true,\n            position: i,\n            value: null\n        };\n    };\n}\nfunction fail(\ndata, i) {\n    return { matched: false };\n}\nfunction error(message) {\n    return (data, i) => {\n        throw new Error((message instanceof Function) ? message(data, i) : message);\n    };\n}\nfunction token(\nonToken,\nonEnd) {\n    return (data, i) => {\n        let position = i;\n        let value = undefined;\n        if (i < data.tokens.length) {\n            value = onToken(data.tokens[i], data, i);\n            if (value !== undefined) {\n                position++;\n            }\n        }\n        else {\n            onEnd?.(data, i);\n        }\n        return (value === undefined)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: position,\n                value: value\n            };\n    };\n}\nfunction any(data, i) {\n    return (i < data.tokens.length)\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction satisfy(\ntest) {\n    return (data, i) => (i < data.tokens.length && test(data.tokens[i], data, i))\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction mapInner(r, f) {\n    return (r.matched) ? ({\n        matched: true,\n        position: r.position,\n        value: f(r.value, r.position)\n    }) : r;\n}\nfunction mapOuter(r, f) {\n    return (r.matched) ? f(r) : r;\n}\nfunction map(p, mapper) {\n    return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p,\nmapper) {\n    return (data, i) => mapOuter(p(data, i), (m) => mapper(m, data, i));\n}\nfunction peek(p, f) {\n    return (data, i) => {\n        const r = p(data, i);\n        f(r, data, i);\n        return r;\n    };\n}\nfunction option(p, def) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? r\n            : {\n                matched: true,\n                position: i,\n                value: def\n            };\n    };\n}\nfunction not(p) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: i,\n                value: true\n            };\n    };\n}\nfunction choice(...ps) {\n    return (data, i) => {\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched) {\n                return result;\n            }\n        }\n        return { matched: false };\n    };\n}\nfunction otherwise(pa, pb) {\n    return (data, i) => {\n        const r1 = pa(data, i);\n        return (r1.matched)\n            ? r1\n            : pb(data, i);\n    };\n}\nfunction longest(...ps) {\n    return (data, i) => {\n        let match = undefined;\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched && (!match || match.position < result.position)) {\n                match = result;\n            }\n        }\n        return match || { matched: false };\n    };\n}\nfunction takeWhile(p,\ntest) {\n    return (data, i) => {\n        const values = [];\n        let success = true;\n        do {\n            const r = p(data, i);\n            if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n                values.push(r.value);\n                i = r.position;\n            }\n            else {\n                success = false;\n            }\n        } while (success);\n        return {\n            matched: true,\n            position: i,\n            value: values\n        };\n    };\n}\nfunction takeUntil(p,\ntest) {\n    return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n    return takeWhile(p, () => true);\n}\nfunction many1(p) {\n    return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n    return ab(pa, pb, (va) => va);\n}\nfunction right(pa, pb) {\n    return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapOuter(pb(data, ma.position), (mb) => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n    return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n    return (data, i) => {\n        const result = [];\n        let position = i;\n        for (const p of ps) {\n            const r1 = p(data, position);\n            if (r1.matched) {\n                result.push(r1.value);\n                position = r1.position;\n            }\n            else {\n                return { matched: false };\n            }\n        }\n        return {\n            matched: true,\n            position: position,\n            value: result\n        };\n    };\n}\nfunction skip(...ps) {\n    return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n    return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n    return map(p, (vs) => vs.flatMap((v) => v));\n}\nfunction sepBy1(pValue, pSep) {\n    return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n    return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc,\nf) {\n    return (data, i) => {\n        let loop = true;\n        let acc1 = acc;\n        let pos = i;\n        do {\n            const r = f(acc1, data, pos)(data, pos);\n            if (r.matched) {\n                acc1 = r.value;\n                pos = r.position;\n            }\n            else {\n                loop = false;\n            }\n        } while (loop);\n        return {\n            matched: true,\n            position: pos,\n            value: acc1\n        };\n    };\n}\nfunction reduceLeft(acc, p,\nreducer) {\n    return chainReduce(acc, (acc) => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc,\nreducer) {\n    return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n    return chain(pLeft, (v0) => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n    return ab(reduceRight(pOper, (y) => y, (f, acc) => (y) => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n    return chain(pLeft, (v0) => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n    return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), (y) => y, ([x, f], acc) => (y) => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n    return (data, i) => (cond(data, i))\n        ? pTrue(data, i)\n        : pFalse(data, i);\n}\nfunction decide(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => m1.value(data, m1.position));\n}\nfunction chain(p,\nf) {\n    return (data, i) => mapOuter(p(data, i), (m1) => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => ({\n        matched: true,\n        position: i,\n        value: m1.value\n    }));\n}\nfunction recursive(f) {\n    return function (data, i) {\n        return f()(data, i);\n    };\n}\nfunction start(data, i) {\n    return (i !== 0)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction end(data, i) {\n    return (i < data.tokens.length)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction remainingTokensNumber(data, i) {\n    return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n    const len = data.tokens.length;\n    const lowIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, i - contextTokens, len - contextTokens);\n    const highIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(contextTokens, i + 1 + contextTokens, len);\n    const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n    const lines = [];\n    const indexWidth = String(highIndex - 1).length + 1;\n    if (i < 0) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    if (0 < lowIndex) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    for (let j = 0; j < tokensSlice.length; j++) {\n        const index = lowIndex + j;\n        lines.push(`${String(index).padStart(indexWidth)} ${(index === i ? '>' : ' ')} ${(0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.escapeWhitespace)(formatToken(tokensSlice[j]))}`);\n    }\n    if (highIndex < len) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    if (len <= i) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n    const data = { tokens: tokens, options: options };\n    const result = parser(data, 0);\n    if (!result.matched) {\n        throw new Error('No match');\n    }\n    if (result.position < data.tokens.length) {\n        throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n    }\n    return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n    const result = parser({ tokens: tokens, options: options }, 0);\n    return (result.matched)\n        ? result.value\n        : undefined;\n}\nfunction match(matcher, tokens, options) {\n    const result = matcher({ tokens: tokens, options: options }, 0);\n    return result.value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peberminta/lib/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/peberminta/lib/util.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/util.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   escapeWhitespace: () => (/* binding */ escapeWhitespace)\n/* harmony export */ });\nfunction clamp(left, x, right) {\n    return Math.max(left, Math.min(x, right));\n}\nfunction escapeWhitespace(str) {\n    return str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\\\t' : r ? '\\\\r' : '\\\\n');\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGViZXJtaW50YS9saWIvdXRpbC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1DIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFxwZWJlcm1pbnRhXFxsaWJcXHV0aWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGNsYW1wKGxlZnQsIHgsIHJpZ2h0KSB7XG4gICAgcmV0dXJuIE1hdGgubWF4KGxlZnQsIE1hdGgubWluKHgsIHJpZ2h0KSk7XG59XG5mdW5jdGlvbiBlc2NhcGVXaGl0ZXNwYWNlKHN0cikge1xuICAgIHJldHVybiBzdHIucmVwbGFjZSgvKFxcdCl8KFxccil8KFxcbikvZywgKG0sIHQsIHIpID0+IHQgPyAnXFxcXHQnIDogciA/ICdcXFxccicgOiAnXFxcXG4nKTtcbn1cblxuZXhwb3J0IHsgY2xhbXAsIGVzY2FwZVdoaXRlc3BhY2UgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peberminta/lib/util.mjs\n");

/***/ })

};
;