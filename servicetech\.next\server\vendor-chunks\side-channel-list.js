"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-list";
exports.ids = ["vendor-chunks/side-channel-list"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-list/index.js":
/*!*************************************************!*\
  !*** ./node_modules/side-channel-list/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-list/index.js\n");

/***/ })

};
;