/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlbuilder";
exports.ids = ["vendor-chunks/xmlbuilder"];
exports.modules = {

/***/ "(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/DocumentPosition.js ***!
  \*********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  module.exports = {\n    Disconnected: 1,\n    Preceding: 2,\n    Following: 4,\n    Contains: 8,\n    ContainedBy: 16,\n    ImplementationSpecific: 32\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvRG9jdW1lbnRQb3NpdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFx4bWxidWlsZGVyXFxsaWJcXERvY3VtZW50UG9zaXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAyLjQuMVxuKGZ1bmN0aW9uKCkge1xuICBtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBEaXNjb25uZWN0ZWQ6IDEsXG4gICAgUHJlY2VkaW5nOiAyLFxuICAgIEZvbGxvd2luZzogNCxcbiAgICBDb250YWluczogOCxcbiAgICBDb250YWluZWRCeTogMTYsXG4gICAgSW1wbGVtZW50YXRpb25TcGVjaWZpYzogMzJcbiAgfTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/NodeType.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/NodeType.js ***!
  \*************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    // Numeric codes up to 200 are reserved to W3C for possible future use.\n    // Following are types internal to this library:\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvTm9kZVR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFx4bWxidWlsZGVyXFxsaWJcXE5vZGVUeXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMi40LjFcbihmdW5jdGlvbigpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgRWxlbWVudDogMSxcbiAgICBBdHRyaWJ1dGU6IDIsXG4gICAgVGV4dDogMyxcbiAgICBDRGF0YTogNCxcbiAgICBFbnRpdHlSZWZlcmVuY2U6IDUsXG4gICAgRW50aXR5RGVjbGFyYXRpb246IDYsXG4gICAgUHJvY2Vzc2luZ0luc3RydWN0aW9uOiA3LFxuICAgIENvbW1lbnQ6IDgsXG4gICAgRG9jdW1lbnQ6IDksXG4gICAgRG9jVHlwZTogMTAsXG4gICAgRG9jdW1lbnRGcmFnbWVudDogMTEsXG4gICAgTm90YXRpb25EZWNsYXJhdGlvbjogMTIsXG4gICAgLy8gTnVtZXJpYyBjb2RlcyB1cCB0byAyMDAgYXJlIHJlc2VydmVkIHRvIFczQyBmb3IgcG9zc2libGUgZnV0dXJlIHVzZS5cbiAgICAvLyBGb2xsb3dpbmcgYXJlIHR5cGVzIGludGVybmFsIHRvIHRoaXMgbGlicmFyeTpcbiAgICBEZWNsYXJhdGlvbjogMjAxLFxuICAgIFJhdzogMjAyLFxuICAgIEF0dHJpYnV0ZURlY2xhcmF0aW9uOiAyMDMsXG4gICAgRWxlbWVudERlY2xhcmF0aW9uOiAyMDQsXG4gICAgRHVtbXk6IDIwNVxuICB9O1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/Utility.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/Utility.js ***!
  \************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Copies all enumerable own properties from `sources` to `target`\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function(target, ...sources) {\n    var i, key, len, source;\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  // Determines if `val` is a Function object\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  // Determines if `val` is an Object\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  // Determines if `val` is an Array\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  // Determines if `val` is an empty Array or an Object with no own properties\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  // Determines if `val` is a plain Object\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  // Gets the primitive value of an object\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/Utility.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/WriterState.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/WriterState.js ***!
  \****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvV3JpdGVyU3RhdGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFx4bWxidWlsZGVyXFxsaWJcXFdyaXRlclN0YXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMi40LjFcbihmdW5jdGlvbigpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgTm9uZTogMCxcbiAgICBPcGVuVGFnOiAxLFxuICAgIEluc2lkZVRhZzogMixcbiAgICBDbG9zZVRhZzogM1xuICB9O1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLAttribute.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLAttribute, XMLNode;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  // Represents an attribute\n  module.exports = XMLAttribute = (function() {\n    class XMLAttribute {\n      // Initializes a new instance of `XMLAttribute`\n\n      // `parent` the parent node\n      // `name` attribute target\n      // `value` attribute value\n      constructor(parent, name, value) {\n        this.parent = parent;\n        if (this.parent) {\n          this.options = this.parent.options;\n          this.stringify = this.parent.stringify;\n        }\n        if (name == null) {\n          throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n        }\n        this.name = this.stringify.name(name);\n        this.value = this.stringify.attValue(value);\n        this.type = NodeType.Attribute;\n        // DOM level 3\n        this.isId = false;\n        this.schemaTypeInfo = null;\n      }\n\n      // Creates and returns a deep clone of `this`\n      clone() {\n        return Object.create(this);\n      }\n\n      // Converts the XML fragment to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n      }\n\n      \n      // Returns debug string for this node\n      debugInfo(name) {\n        name = name || this.name;\n        if (name == null) {\n          return \"parent: <\" + this.parent.name + \">\";\n        } else {\n          return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n        }\n      }\n\n      isEqualNode(node) {\n        if (node.namespaceURI !== this.namespaceURI) {\n          return false;\n        }\n        if (node.prefix !== this.prefix) {\n          return false;\n        }\n        if (node.localName !== this.localName) {\n          return false;\n        }\n        if (node.value !== this.value) {\n          return false;\n        }\n        return true;\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    // DOM level 3\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    // DOM level 4\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function() {\n        return true;\n      }\n    });\n\n    return XMLAttribute;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCData.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLCData, XMLCharacterData;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  // Represents a  CDATA node\n  module.exports = XMLCData = class XMLCData extends XMLCharacterData {\n    // Initializes a new instance of `XMLCData`\n\n    // `text` CDATA text\n    constructor(parent, text) {\n      super(parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n\n    // Creates and returns a deep clone of `this`\n    clone() {\n      return Object.create(this);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCharacterData.js ***!
  \*********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var XMLCharacterData, XMLNode;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  // Represents a character data node\n  module.exports = XMLCharacterData = (function() {\n    class XMLCharacterData extends XMLNode {\n      // Initializes a new instance of `XMLCharacterData`\n\n      constructor(parent) {\n        super(parent);\n        this.value = '';\n      }\n\n      \n      // Creates and returns a deep clone of `this`\n      clone() {\n        return Object.create(this);\n      }\n\n      // DOM level 1 functions to be implemented later\n      substringData(offset, count) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      appendData(arg) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      insertData(offset, arg) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      deleteData(offset, count) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      replaceData(offset, count, arg) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      isEqualNode(node) {\n        if (!super.isEqualNode(node)) {\n          return false;\n        }\n        if (node.data !== this.data) {\n          return false;\n        }\n        return true;\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function() {\n        return this.value.length;\n      }\n    });\n\n    // DOM level 3\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    return XMLCharacterData;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLComment.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLCharacterData, XMLComment;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  // Represents a comment node\n  module.exports = XMLComment = class XMLComment extends XMLCharacterData {\n    // Initializes a new instance of `XMLComment`\n\n    // `text` comment text\n    constructor(parent, text) {\n      super(parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n\n    // Creates and returns a deep clone of `this`\n    clone() {\n      return Object.create(this);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js":
/*!************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var XMLDOMConfiguration, XMLDOMErrorHandler, XMLDOMStringList;\n\n  XMLDOMErrorHandler = __webpack_require__(/*! ./XMLDOMErrorHandler */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\");\n\n  XMLDOMStringList = __webpack_require__(/*! ./XMLDOMStringList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js\");\n\n  // Implements the DOMConfiguration interface\n  module.exports = XMLDOMConfiguration = (function() {\n    class XMLDOMConfiguration {\n      constructor() {\n        var clonedSelf;\n        this.defaultParams = {\n          \"canonical-form\": false,\n          \"cdata-sections\": false,\n          \"comments\": false,\n          \"datatype-normalization\": false,\n          \"element-content-whitespace\": true,\n          \"entities\": true,\n          \"error-handler\": new XMLDOMErrorHandler(),\n          \"infoset\": true,\n          \"validate-if-schema\": false,\n          \"namespaces\": true,\n          \"namespace-declarations\": true,\n          \"normalize-characters\": false,\n          \"schema-location\": '',\n          \"schema-type\": '',\n          \"split-cdata-sections\": true,\n          \"validate\": false,\n          \"well-formed\": true\n        };\n        this.params = clonedSelf = Object.create(this.defaultParams);\n      }\n\n      // Gets the value of a parameter.\n\n      // `name` name of the parameter\n      getParameter(name) {\n        if (this.params.hasOwnProperty(name)) {\n          return this.params[name];\n        } else {\n          return null;\n        }\n      }\n\n      // Checks if setting a parameter to a specific value is supported.\n\n      // `name` name of the parameter\n      // `value` parameter value\n      canSetParameter(name, value) {\n        return true;\n      }\n\n      // Sets the value of a parameter.\n\n      // `name` name of the parameter\n      // `value` new value or null if the user wishes to unset the parameter\n      setParameter(name, value) {\n        if (value != null) {\n          return this.params[name] = value;\n        } else {\n          return delete this.params[name];\n        }\n      }\n\n    };\n\n    // Returns the list of parameter names\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function() {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n\n    return XMLDOMConfiguration;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js":
/*!***********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js ***!
  \***********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Represents the error handler for DOM operations\n  var XMLDOMErrorHandler;\n\n  module.exports = XMLDOMErrorHandler = class XMLDOMErrorHandler {\n    // Initializes a new instance of `XMLDOMErrorHandler`\n\n    constructor() {}\n\n    // Called on the error handler when an error occurs.\n\n    // `error` the error message as a string\n    handleError(error) {\n      throw new Error(error);\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NRXJyb3JIYW5kbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xceG1sYnVpbGRlclxcbGliXFxYTUxET01FcnJvckhhbmRsZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAyLjQuMVxuKGZ1bmN0aW9uKCkge1xuICAvLyBSZXByZXNlbnRzIHRoZSBlcnJvciBoYW5kbGVyIGZvciBET00gb3BlcmF0aW9uc1xuICB2YXIgWE1MRE9NRXJyb3JIYW5kbGVyO1xuXG4gIG1vZHVsZS5leHBvcnRzID0gWE1MRE9NRXJyb3JIYW5kbGVyID0gY2xhc3MgWE1MRE9NRXJyb3JIYW5kbGVyIHtcbiAgICAvLyBJbml0aWFsaXplcyBhIG5ldyBpbnN0YW5jZSBvZiBgWE1MRE9NRXJyb3JIYW5kbGVyYFxuXG4gICAgY29uc3RydWN0b3IoKSB7fVxuXG4gICAgLy8gQ2FsbGVkIG9uIHRoZSBlcnJvciBoYW5kbGVyIHdoZW4gYW4gZXJyb3Igb2NjdXJzLlxuXG4gICAgLy8gYGVycm9yYCB0aGUgZXJyb3IgbWVzc2FnZSBhcyBhIHN0cmluZ1xuICAgIGhhbmRsZUVycm9yKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IpO1xuICAgIH1cblxuICB9O1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js":
/*!*************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMImplementation.js ***!
  \*************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Implements the DOMImplementation interface\n  var XMLDOMImplementation;\n\n  module.exports = XMLDOMImplementation = class XMLDOMImplementation {\n    // Tests if the DOM implementation implements a specific feature.\n\n    // `feature` package name of the feature to test. In Level 1, the\n    //           legal values are \"HTML\" and \"XML\" (case-insensitive).\n    // `version` version number of the package name to test. \n    //           In Level 1, this is the string \"1.0\". If the version is \n    //           not specified, supporting any version of the feature will \n    //           cause the method to return true.\n    hasFeature(feature, version) {\n      return true;\n    }\n\n    // Creates a new document type declaration.\n\n    // `qualifiedName` qualified name of the document type to be created\n    // `publicId` public identifier of the external subset\n    // `systemId` system identifier of the external subset\n    createDocumentType(qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    }\n\n    // Creates a new document.\n\n    // `namespaceURI` namespace URI of the document element to create\n    // `qualifiedName` the qualified name of the document to be created\n    // `doctype` the type of document to be created or null\n    createDocument(namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    }\n\n    // Creates a new HTML document.\n\n    // `title` document title\n    createHTMLDocument(title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    }\n\n    // Returns a specialized object which implements the specialized APIs \n    // of the specified feature and version.\n\n    // `feature` name of the feature requested.\n    // `version` version number of the feature to test\n    getFeature(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NSW1wbGVtZW50YXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xceG1sYnVpbGRlclxcbGliXFxYTUxET01JbXBsZW1lbnRhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDIuNC4xXG4oZnVuY3Rpb24oKSB7XG4gIC8vIEltcGxlbWVudHMgdGhlIERPTUltcGxlbWVudGF0aW9uIGludGVyZmFjZVxuICB2YXIgWE1MRE9NSW1wbGVtZW50YXRpb247XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxET01JbXBsZW1lbnRhdGlvbiA9IGNsYXNzIFhNTERPTUltcGxlbWVudGF0aW9uIHtcbiAgICAvLyBUZXN0cyBpZiB0aGUgRE9NIGltcGxlbWVudGF0aW9uIGltcGxlbWVudHMgYSBzcGVjaWZpYyBmZWF0dXJlLlxuXG4gICAgLy8gYGZlYXR1cmVgIHBhY2thZ2UgbmFtZSBvZiB0aGUgZmVhdHVyZSB0byB0ZXN0LiBJbiBMZXZlbCAxLCB0aGVcbiAgICAvLyAgICAgICAgICAgbGVnYWwgdmFsdWVzIGFyZSBcIkhUTUxcIiBhbmQgXCJYTUxcIiAoY2FzZS1pbnNlbnNpdGl2ZSkuXG4gICAgLy8gYHZlcnNpb25gIHZlcnNpb24gbnVtYmVyIG9mIHRoZSBwYWNrYWdlIG5hbWUgdG8gdGVzdC4gXG4gICAgLy8gICAgICAgICAgIEluIExldmVsIDEsIHRoaXMgaXMgdGhlIHN0cmluZyBcIjEuMFwiLiBJZiB0aGUgdmVyc2lvbiBpcyBcbiAgICAvLyAgICAgICAgICAgbm90IHNwZWNpZmllZCwgc3VwcG9ydGluZyBhbnkgdmVyc2lvbiBvZiB0aGUgZmVhdHVyZSB3aWxsIFxuICAgIC8vICAgICAgICAgICBjYXVzZSB0aGUgbWV0aG9kIHRvIHJldHVybiB0cnVlLlxuICAgIGhhc0ZlYXR1cmUoZmVhdHVyZSwgdmVyc2lvbikge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlcyBhIG5ldyBkb2N1bWVudCB0eXBlIGRlY2xhcmF0aW9uLlxuXG4gICAgLy8gYHF1YWxpZmllZE5hbWVgIHF1YWxpZmllZCBuYW1lIG9mIHRoZSBkb2N1bWVudCB0eXBlIHRvIGJlIGNyZWF0ZWRcbiAgICAvLyBgcHVibGljSWRgIHB1YmxpYyBpZGVudGlmaWVyIG9mIHRoZSBleHRlcm5hbCBzdWJzZXRcbiAgICAvLyBgc3lzdGVtSWRgIHN5c3RlbSBpZGVudGlmaWVyIG9mIHRoZSBleHRlcm5hbCBzdWJzZXRcbiAgICBjcmVhdGVEb2N1bWVudFR5cGUocXVhbGlmaWVkTmFtZSwgcHVibGljSWQsIHN5c3RlbUlkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGVzIGEgbmV3IGRvY3VtZW50LlxuXG4gICAgLy8gYG5hbWVzcGFjZVVSSWAgbmFtZXNwYWNlIFVSSSBvZiB0aGUgZG9jdW1lbnQgZWxlbWVudCB0byBjcmVhdGVcbiAgICAvLyBgcXVhbGlmaWVkTmFtZWAgdGhlIHF1YWxpZmllZCBuYW1lIG9mIHRoZSBkb2N1bWVudCB0byBiZSBjcmVhdGVkXG4gICAgLy8gYGRvY3R5cGVgIHRoZSB0eXBlIG9mIGRvY3VtZW50IHRvIGJlIGNyZWF0ZWQgb3IgbnVsbFxuICAgIGNyZWF0ZURvY3VtZW50KG5hbWVzcGFjZVVSSSwgcXVhbGlmaWVkTmFtZSwgZG9jdHlwZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlcyBhIG5ldyBIVE1MIGRvY3VtZW50LlxuXG4gICAgLy8gYHRpdGxlYCBkb2N1bWVudCB0aXRsZVxuICAgIGNyZWF0ZUhUTUxEb2N1bWVudCh0aXRsZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfVxuXG4gICAgLy8gUmV0dXJucyBhIHNwZWNpYWxpemVkIG9iamVjdCB3aGljaCBpbXBsZW1lbnRzIHRoZSBzcGVjaWFsaXplZCBBUElzIFxuICAgIC8vIG9mIHRoZSBzcGVjaWZpZWQgZmVhdHVyZSBhbmQgdmVyc2lvbi5cblxuICAgIC8vIGBmZWF0dXJlYCBuYW1lIG9mIHRoZSBmZWF0dXJlIHJlcXVlc3RlZC5cbiAgICAvLyBgdmVyc2lvbmAgdmVyc2lvbiBudW1iZXIgb2YgdGhlIGZlYXR1cmUgdG8gdGVzdFxuICAgIGdldEZlYXR1cmUoZmVhdHVyZSwgdmVyc2lvbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfVxuXG4gIH07XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMStringList.js ***!
  \*********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Represents a list of string entries\n  var XMLDOMStringList;\n\n  module.exports = XMLDOMStringList = (function() {\n    class XMLDOMStringList {\n      // Initializes a new instance of `XMLDOMStringList`\n      // This is just a wrapper around an ordinary\n      // JS array.\n\n      // `arr` the array of string values\n      constructor(arr) {\n        this.arr = arr || [];\n      }\n\n      // Returns the indexth item in the collection.\n\n      // `index` index into the collection\n      item(index) {\n        return this.arr[index] || null;\n      }\n\n      // Test if a string is part of this DOMStringList.\n\n      // `str` the string to look for\n      contains(str) {\n        return this.arr.indexOf(str) !== -1;\n      }\n\n    };\n\n    // Returns the number of strings in the list.\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function() {\n        return this.arr.length;\n      }\n    });\n\n    return XMLDOMStringList;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDAttList.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDTDAttList, XMLNode;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents an attribute list\n  module.exports = XMLDTDAttList = class XMLDTDAttList extends XMLNode {\n    // Initializes a new instance of `XMLDTDAttList`\n\n    // `parent` the parent `XMLDocType` element\n    // `elementName` the name of the element containing this attribute\n    // `attributeName` attribute name\n    // `attributeType` type of the attribute\n    // `defaultValueType` default value type (either #REQUIRED, #IMPLIED,\n    //                    #FIXED or #DEFAULT)\n    // `defaultValue` default value of the attribute\n    //                (only used for #FIXED or #DEFAULT)\n    constructor(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      super(parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDElement.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDTDElement, XMLNode;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents an attribute\n  module.exports = XMLDTDElement = class XMLDTDElement extends XMLNode {\n    // Initializes a new instance of `XMLDTDElement`\n\n    // `parent` the parent `XMLDocType` element\n    // `name` element name\n    // `value` element content (defaults to #PCDATA)\n    constructor(parent, name, value) {\n      super(parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDEntity.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDTDEntity, XMLNode, isObject;\n\n  ({isObject} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents an entity declaration in the DTD\n  module.exports = XMLDTDEntity = (function() {\n    class XMLDTDEntity extends XMLNode {\n      // Initializes a new instance of `XMLDTDEntity`\n\n      // `parent` the parent `XMLDocType` element\n      // `pe` whether this is a parameter entity or a general entity\n      //      defaults to `false` (general entity)\n      // `name` the name of the entity\n      // `value` internal entity value or an object with external entity details\n      // `value.pubID` public identifier\n      // `value.sysID` system identifier\n      // `value.nData` notation declaration\n      constructor(parent, pe, name, value) {\n        super(parent);\n        if (name == null) {\n          throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n        }\n        if (value == null) {\n          throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n        }\n        this.pe = !!pe;\n        this.name = this.stringify.name(name);\n        this.type = NodeType.EntityDeclaration;\n        if (!isObject(value)) {\n          this.value = this.stringify.dtdEntityValue(value);\n          this.internal = true;\n        } else {\n          if (!value.pubID && !value.sysID) {\n            throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n          }\n          if (value.pubID && !value.sysID) {\n            throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n          }\n          this.internal = false;\n          if (value.pubID != null) {\n            this.pubID = this.stringify.dtdPubID(value.pubID);\n          }\n          if (value.sysID != null) {\n            this.sysID = this.stringify.dtdSysID(value.sysID);\n          }\n          if (value.nData != null) {\n            this.nData = this.stringify.dtdNData(value.nData);\n          }\n          if (this.pe && this.nData) {\n            throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n          }\n        }\n      }\n\n      // Converts the XML fragment to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function() {\n        return this.nData || null;\n      }\n    });\n\n    // DOM level 3\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function() {\n        return null;\n      }\n    });\n\n    return XMLDTDEntity;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDNotation.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDTDNotation, XMLNode;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents a NOTATION entry in the DTD\n  module.exports = XMLDTDNotation = (function() {\n    class XMLDTDNotation extends XMLNode {\n      // Initializes a new instance of `XMLDTDNotation`\n\n      // `parent` the parent `XMLDocType` element\n      // `name` the name of the notation\n      // `value` an object with external entity details\n      // `value.pubID` public identifier\n      // `value.sysID` system identifier\n      constructor(parent, name, value) {\n        super(parent);\n        if (name == null) {\n          throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n        }\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        this.name = this.stringify.name(name);\n        this.type = NodeType.NotationDeclaration;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n      }\n\n      // Converts the XML fragment to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    return XMLDTDNotation;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDeclaration.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDeclaration, XMLNode, isObject;\n\n  ({isObject} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents the XML declaration\n  module.exports = XMLDeclaration = class XMLDeclaration extends XMLNode {\n    // Initializes a new instance of `XMLDeclaration`\n\n    // `parent` the document object\n\n    // `version` A version number string, e.g. 1.0\n    // `encoding` Encoding declaration, e.g. UTF-8\n    // `standalone` standalone document declaration: true or false\n    constructor(parent, version, encoding, standalone) {\n      super(parent);\n      // arguments may also be passed as an object\n      if (isObject(version)) {\n        ({version, encoding, standalone} = version);\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    // Converts to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocType.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNamedNodeMap, XMLNode, isObject;\n\n  ({isObject} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  // Represents doctype declaration\n  module.exports = XMLDocType = (function() {\n    class XMLDocType extends XMLNode {\n      // Initializes a new instance of `XMLDocType`\n\n      // `parent` the document object\n\n      // `pubID` public identifier of the external subset\n      // `sysID` system identifier of the external subset\n      constructor(parent, pubID, sysID) {\n        var child, i, len, ref;\n        super(parent);\n        this.type = NodeType.DocType;\n        // set DTD name to the name of the root node\n        if (parent.children) {\n          ref = parent.children;\n          for (i = 0, len = ref.length; i < len; i++) {\n            child = ref[i];\n            if (child.type === NodeType.Element) {\n              this.name = child.name;\n              break;\n            }\n          }\n        }\n        this.documentObject = parent;\n        // arguments may also be passed as an object\n        if (isObject(pubID)) {\n          ({pubID, sysID} = pubID);\n        }\n        if (sysID == null) {\n          [sysID, pubID] = [pubID, sysID];\n        }\n        if (pubID != null) {\n          this.pubID = this.stringify.dtdPubID(pubID);\n        }\n        if (sysID != null) {\n          this.sysID = this.stringify.dtdSysID(sysID);\n        }\n      }\n\n      // Creates an element type declaration\n\n      // `name` element name\n      // `value` element content (defaults to #PCDATA)\n      element(name, value) {\n        var child;\n        child = new XMLDTDElement(this, name, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates an attribute declaration\n\n      // `elementName` the name of the element containing this attribute\n      // `attributeName` attribute name\n      // `attributeType` type of the attribute (defaults to CDATA)\n      // `defaultValueType` default value type (either #REQUIRED, #IMPLIED, #FIXED or\n      //                    #DEFAULT) (defaults to #IMPLIED)\n      // `defaultValue` default value of the attribute\n      //                (only used for #FIXED or #DEFAULT)\n      attList(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n        var child;\n        child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a general entity declaration\n\n      // `name` the name of the entity\n      // `value` internal entity value or an object with external entity details\n      // `value.pubID` public identifier\n      // `value.sysID` system identifier\n      // `value.nData` notation declaration\n      entity(name, value) {\n        var child;\n        child = new XMLDTDEntity(this, false, name, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a parameter entity declaration\n\n      // `name` the name of the entity\n      // `value` internal entity value or an object with external entity details\n      // `value.pubID` public identifier\n      // `value.sysID` system identifier\n      pEntity(name, value) {\n        var child;\n        child = new XMLDTDEntity(this, true, name, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a NOTATION declaration\n\n      // `name` the name of the notation\n      // `value` an object with external entity details\n      // `value.pubID` public identifier\n      // `value.sysID` system identifier\n      notation(name, value) {\n        var child;\n        child = new XMLDTDNotation(this, name, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Converts to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.docType(this, this.options.writer.filterOptions(options));\n      }\n\n      // Aliases\n      ele(name, value) {\n        return this.element(name, value);\n      }\n\n      att(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n        return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      }\n\n      ent(name, value) {\n        return this.entity(name, value);\n      }\n\n      pent(name, value) {\n        return this.pEntity(name, value);\n      }\n\n      not(name, value) {\n        return this.notation(name, value);\n      }\n\n      up() {\n        return this.root() || this.documentObject;\n      }\n\n      isEqualNode(node) {\n        if (!super.isEqualNode(node)) {\n          return false;\n        }\n        if (node.name !== this.name) {\n          return false;\n        }\n        if (node.publicId !== this.publicId) {\n          return false;\n        }\n        if (node.systemId !== this.systemId) {\n          return false;\n        }\n        return true;\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLDocType.prototype, 'entities', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if ((child.type === NodeType.EntityDeclaration) && !child.pe) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'notations', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.NotationDeclaration) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    // DOM level 2\n    Object.defineProperty(XMLDocType.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'internalSubset', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    return XMLDocType;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocument.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDOMConfiguration, XMLDOMImplementation, XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject;\n\n  ({isPlainObject} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDOMConfiguration = __webpack_require__(/*! ./XMLDOMConfiguration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  // Represents an XML builder\n  module.exports = XMLDocument = (function() {\n    class XMLDocument extends XMLNode {\n      // Initializes a new instance of `XMLDocument`\n\n      // `options.keepNullNodes` whether nodes with null values will be kept\n      //     or ignored: true or false\n      // `options.keepNullAttributes` whether attributes with null values will be\n      //     kept or ignored: true or false\n      // `options.ignoreDecorators` whether decorator strings will be ignored when\n      //     converting JS objects: true or false\n      // `options.separateArrayItems` whether array items are created as separate\n      //     nodes when passed as an object value: true or false\n      // `options.noDoubleEncoding` whether existing html entities are encoded:\n      //     true or false\n      // `options.stringify` a set of functions to use for converting values to\n      //     strings\n      // `options.writer` the default XML writer to use for converting nodes to\n      //     string. If the default writer is not set, the built-in XMLStringWriter\n      //     will be used instead.\n      constructor(options) {\n        super(null);\n        this.name = \"#document\";\n        this.type = NodeType.Document;\n        this.documentURI = null;\n        this.domConfig = new XMLDOMConfiguration();\n        options || (options = {});\n        if (!options.writer) {\n          options.writer = new XMLStringWriter();\n        }\n        this.options = options;\n        this.stringify = new XMLStringifier(options);\n      }\n\n      // Ends the document and passes it to the given XML writer\n\n      // `writer` is either an XML writer or a plain object to pass to the\n      // constructor of the default XML writer. The default writer is assigned when\n      // creating the XML document. Following flags are recognized by the\n      // built-in XMLStringWriter:\n      //   `writer.pretty` pretty prints the result\n      //   `writer.indent` indentation for pretty print\n      //   `writer.offset` how many indentations to add to every line for pretty print\n      //   `writer.newline` newline sequence for pretty print\n      end(writer) {\n        var writerOptions;\n        writerOptions = {};\n        if (!writer) {\n          writer = this.options.writer;\n        } else if (isPlainObject(writer)) {\n          writerOptions = writer;\n          writer = this.options.writer;\n        }\n        return writer.document(this, writer.filterOptions(writerOptions));\n      }\n\n      // Converts the XML document to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.document(this, this.options.writer.filterOptions(options));\n      }\n\n      // DOM level 1 functions to be implemented later\n      createElement(tagName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createDocumentFragment() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createTextNode(data) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createComment(data) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createCDATASection(data) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createProcessingInstruction(target, data) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createAttribute(name) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createEntityReference(name) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByTagName(tagname) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM level 2 functions to be implemented later\n      importNode(importedNode, deep) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createElementNS(namespaceURI, qualifiedName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createAttributeNS(namespaceURI, qualifiedName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByTagNameNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementById(elementId) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM level 3 functions to be implemented later\n      adoptNode(source) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      normalizeDocument() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      renameNode(node, namespaceURI, qualifiedName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM level 4 functions to be implemented later\n      getElementsByClassName(classNames) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createEvent(eventInterface) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createRange() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createNodeIterator(root, whatToShow, filter) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      createTreeWalker(root, whatToShow, filter) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLDocument.prototype, 'implementation', {\n      value: new XMLDOMImplementation()\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'doctype', {\n      get: function() {\n        var child, i, len, ref;\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.DocType) {\n            return child;\n          }\n        }\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'documentElement', {\n      get: function() {\n        return this.rootObject || null;\n      }\n    });\n\n    // DOM level 3\n    Object.defineProperty(XMLDocument.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'strictErrorChecking', {\n      get: function() {\n        return false;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlEncoding', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].encoding;\n        } else {\n          return null;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlStandalone', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].standalone === 'yes';\n        } else {\n          return false;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlVersion', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].version;\n        } else {\n          return \"1.0\";\n        }\n      }\n    });\n\n    // DOM level 4\n    Object.defineProperty(XMLDocument.prototype, 'URL', {\n      get: function() {\n        return this.documentURI;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'origin', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'compatMode', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'characterSet', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'contentType', {\n      get: function() {\n        return null;\n      }\n    });\n\n    return XMLDocument;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocumentCB.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, WriterState, XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocument, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject,\n    hasProp = {}.hasOwnProperty;\n\n  ({isObject, isFunction, isPlainObject, getValue} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  // Represents an XML builder\n  module.exports = XMLDocumentCB = class XMLDocumentCB {\n    // Initializes a new instance of `XMLDocumentCB`\n\n    // `options.keepNullNodes` whether nodes with null values will be kept\n    //     or ignored: true or false\n    // `options.keepNullAttributes` whether attributes with null values will be\n    //     kept or ignored: true or false\n    // `options.ignoreDecorators` whether decorator strings will be ignored when\n    //     converting JS objects: true or false\n    // `options.separateArrayItems` whether array items are created as separate\n    //     nodes when passed as an object value: true or false\n    // `options.noDoubleEncoding` whether existing html entities are encoded:\n    //     true or false\n    // `options.stringify` a set of functions to use for converting values to\n    //     strings\n    // `options.writer` the default XML writer to use for converting nodes to\n    //     string. If the default writer is not set, the built-in XMLStringWriter\n    //     will be used instead.\n\n    // `onData` the function to be called when a new chunk of XML is output. The\n    //          string containing the XML chunk is passed to `onData` as its first\n    //          argument, and the current indentation level as its second argument.\n    // `onEnd`  the function to be called when the XML document is completed with\n    //          `end`. `onEnd` does not receive any arguments.\n    constructor(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    // Creates a child element node from the given XMLNode\n\n    // `node` the child node\n    createChildNode(node) {\n      var att, attName, attributes, child, i, len, ref, ref1;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref = node.attribs;\n          for (attName in ref) {\n            if (!hasProp.call(ref, attName)) continue;\n            att = ref[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref1 = node.children;\n      // write child nodes recursively\n      for (i = 0, len = ref1.length; i < len; i++) {\n        child = ref1[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    }\n\n    // Creates a dummy node\n\n    dummy() {\n      // no-op, just return this\n      return this;\n    }\n\n    // Creates a node\n\n    // `name` name of the node\n    // `attributes` an object containing name/value pairs of attributes\n    // `text` element text\n    node(name, attributes, text) {\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      // swap argument order: text <-> attributes\n      if (!isObject(attributes)) {\n        [text, attributes] = [attributes, text];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    }\n\n    // Creates a child element node or an element type declaration when called\n    // inside the DTD\n\n    // `name` name of the node\n    // `attributes` an object containing name/value pairs of attributes\n    // `text` element text\n    element(name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement(...arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref = root.children;\n          for (i = 0, len = ref.length; i < len; i++) {\n            child = ref[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    }\n\n    // Adds or modifies an attribute\n\n    // `name` attribute name\n    // `value` attribute value\n    attribute(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) { // expand if object\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    }\n\n    // Creates a text node\n\n    // `value` element text\n    text(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates a CDATA node\n\n    // `value` element text without CDATA delimiters\n    cdata(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates a comment node\n\n    // `value` comment text\n    comment(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Adds unescaped raw text\n\n    // `value` text\n    raw(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Adds a processing instruction\n\n    // `target` instruction target\n    // `value` instruction value\n    instruction(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) { // expand if array\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) { // expand if object\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    }\n\n    // Creates the xml declaration\n\n    // `version` A version number string, e.g. 1.0\n    // `encoding` Encoding declaration, e.g. UTF-8\n    // `standalone` standalone document declaration: true or false\n    declaration(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates the document type declaration\n\n    // `root`  the name of the root node\n    // `pubID` the public identifier of the external subset\n    // `sysID` the system identifier of the external subset\n    doctype(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    }\n\n    // Creates an element type declaration\n\n    // `name` element name\n    // `value` element content (defaults to #PCDATA)\n    dtdElement(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates an attribute declaration\n\n    // `elementName` the name of the element containing this attribute\n    // `attributeName` attribute name\n    // `attributeType` type of the attribute (defaults to CDATA)\n    // `defaultValueType` default value type (either #REQUIRED, #IMPLIED, #FIXED or\n    //                    #DEFAULT) (defaults to #IMPLIED)\n    // `defaultValue` default value of the attribute\n    //                (only used for #FIXED or #DEFAULT)\n    attList(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates a general entity declaration\n\n    // `name` the name of the entity\n    // `value` internal entity value or an object with external entity details\n    // `value.pubID` public identifier\n    // `value.sysID` system identifier\n    // `value.nData` notation declaration\n    entity(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates a parameter entity declaration\n\n    // `name` the name of the entity\n    // `value` internal entity value or an object with external entity details\n    // `value.pubID` public identifier\n    // `value.sysID` system identifier\n    pEntity(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Creates a NOTATION declaration\n\n    // `name` the name of the notation\n    // `value` an object with external entity details\n    // `value.pubID` public identifier\n    // `value.sysID` system identifier\n    notation(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    }\n\n    // Gets the parent node\n    up() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    }\n\n    // Ends the document\n    end() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    }\n\n    // Opens the current parent node\n    openCurrent() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    }\n\n    // Writes the opening tag of the current node or the entire node if it has\n    // no child nodes\n    openNode(node) {\n      var att, chunk, name, ref;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref = node.attribs;\n          for (name in ref) {\n            if (!hasProp.call(ref, name)) continue;\n            att = ref[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag; // if node.type is NodeType.DocType\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          \n          // external identifier\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          \n          // internal subset\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    }\n\n    // Writes the closing tag of the current node\n    closeNode(node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel); // if node.type is NodeType.DocType\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    }\n\n    // Called when a new chunk of XML is output\n\n    // `chunk` a string containing the XML chunk\n    // `level` current indentation level\n    onData(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    }\n\n    // Called when the XML document is completed\n    onEnd() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    }\n\n    // Returns debug string\n    debugInfo(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    }\n\n    // Node aliases\n    ele() {\n      return this.element(...arguments);\n    }\n\n    nod(name, attributes, text) {\n      return this.node(name, attributes, text);\n    }\n\n    txt(value) {\n      return this.text(value);\n    }\n\n    dat(value) {\n      return this.cdata(value);\n    }\n\n    com(value) {\n      return this.comment(value);\n    }\n\n    ins(target, value) {\n      return this.instruction(target, value);\n    }\n\n    dec(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    }\n\n    dtd(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    }\n\n    e(name, attributes, text) {\n      return this.element(name, attributes, text);\n    }\n\n    n(name, attributes, text) {\n      return this.node(name, attributes, text);\n    }\n\n    t(value) {\n      return this.text(value);\n    }\n\n    d(value) {\n      return this.cdata(value);\n    }\n\n    c(value) {\n      return this.comment(value);\n    }\n\n    r(value) {\n      return this.raw(value);\n    }\n\n    i(target, value) {\n      return this.instruction(target, value);\n    }\n\n    // Attribute aliases\n    att() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList(...arguments);\n      } else {\n        return this.attribute(...arguments);\n      }\n    }\n\n    a() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList(...arguments);\n      } else {\n        return this.attribute(...arguments);\n      }\n    }\n\n    // DTD aliases\n    // att() and ele() are defined above\n    ent(name, value) {\n      return this.entity(name, value);\n    }\n\n    pent(name, value) {\n      return this.pEntity(name, value);\n    }\n\n    not(name, value) {\n      return this.notation(name, value);\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDummy.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLDummy, XMLNode;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  // Represents a  raw node\n  module.exports = XMLDummy = class XMLDummy extends XMLNode {\n    // Initializes a new instance of `XMLDummy`\n\n    // `XMLDummy` is a special node representing a node with \n    // a null value. Dummy nodes are created while recursively\n    // building the XML tree. Simply skipping null values doesn't\n    // work because that would break the recursive chain.\n    constructor(parent) {\n      super(parent);\n      this.type = NodeType.Dummy;\n    }\n\n    // Creates and returns a deep clone of `this`\n    clone() {\n      return Object.create(this);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return '';\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLElement.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLAttribute, XMLElement, XMLNamedNodeMap, XMLNode, getValue, isFunction, isObject,\n    hasProp = {}.hasOwnProperty;\n\n  ({isObject, isFunction, getValue} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  // Represents an element of the XML document\n  module.exports = XMLElement = (function() {\n    class XMLElement extends XMLNode {\n      // Initializes a new instance of `XMLElement`\n\n      // `parent` the parent node\n      // `name` element name\n      // `attributes` an object containing name/value pairs of attributes\n      constructor(parent, name, attributes) {\n        var child, j, len, ref;\n        super(parent);\n        if (name == null) {\n          throw new Error(\"Missing element name. \" + this.debugInfo());\n        }\n        this.name = this.stringify.name(name);\n        this.type = NodeType.Element;\n        this.attribs = {};\n        this.schemaTypeInfo = null;\n        if (attributes != null) {\n          this.attribute(attributes);\n        }\n        // set properties if this is the root node\n        if (parent.type === NodeType.Document) {\n          this.isRoot = true;\n          this.documentObject = parent;\n          parent.rootObject = this;\n          // set dtd name\n          if (parent.children) {\n            ref = parent.children;\n            for (j = 0, len = ref.length; j < len; j++) {\n              child = ref[j];\n              if (child.type === NodeType.DocType) {\n                child.name = this.name;\n                break;\n              }\n            }\n          }\n        }\n      }\n\n      // Creates and returns a deep clone of `this`\n\n      clone() {\n        var att, attName, clonedSelf, ref;\n        clonedSelf = Object.create(this);\n        // remove document element\n        if (clonedSelf.isRoot) {\n          clonedSelf.documentObject = null;\n        }\n        // clone attributes\n        clonedSelf.attribs = {};\n        ref = this.attribs;\n        for (attName in ref) {\n          if (!hasProp.call(ref, attName)) continue;\n          att = ref[attName];\n          clonedSelf.attribs[attName] = att.clone();\n        }\n        // clone child nodes\n        clonedSelf.children = [];\n        this.children.forEach(function(child) {\n          var clonedChild;\n          clonedChild = child.clone();\n          clonedChild.parent = clonedSelf;\n          return clonedSelf.children.push(clonedChild);\n        });\n        return clonedSelf;\n      }\n\n      // Adds or modifies an attribute\n\n      // `name` attribute name\n      // `value` attribute value\n      attribute(name, value) {\n        var attName, attValue;\n        if (name != null) {\n          name = getValue(name);\n        }\n        if (isObject(name)) { // expand if object\n          for (attName in name) {\n            if (!hasProp.call(name, attName)) continue;\n            attValue = name[attName];\n            this.attribute(attName, attValue);\n          }\n        } else {\n          if (isFunction(value)) {\n            value = value.apply();\n          }\n          if (this.options.keepNullAttributes && (value == null)) {\n            this.attribs[name] = new XMLAttribute(this, name, \"\");\n          } else if (value != null) {\n            this.attribs[name] = new XMLAttribute(this, name, value);\n          }\n        }\n        return this;\n      }\n\n      // Removes an attribute\n\n      // `name` attribute name\n      removeAttribute(name) {\n        var attName, j, len;\n        // Also defined in DOM level 1\n        // removeAttribute(name) removes an attribute by name.\n        if (name == null) {\n          throw new Error(\"Missing attribute name. \" + this.debugInfo());\n        }\n        name = getValue(name);\n        if (Array.isArray(name)) { // expand if array\n          for (j = 0, len = name.length; j < len; j++) {\n            attName = name[j];\n            delete this.attribs[attName];\n          }\n        } else {\n          delete this.attribs[name];\n        }\n        return this;\n      }\n\n      // Converts the XML fragment to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      // `options.allowEmpty` do not self close empty element tags\n      toString(options) {\n        return this.options.writer.element(this, this.options.writer.filterOptions(options));\n      }\n\n      // Aliases\n      att(name, value) {\n        return this.attribute(name, value);\n      }\n\n      a(name, value) {\n        return this.attribute(name, value);\n      }\n\n      // DOM Level 1\n      getAttribute(name) {\n        if (this.attribs.hasOwnProperty(name)) {\n          return this.attribs[name].value;\n        } else {\n          return null;\n        }\n      }\n\n      setAttribute(name, value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getAttributeNode(name) {\n        if (this.attribs.hasOwnProperty(name)) {\n          return this.attribs[name];\n        } else {\n          return null;\n        }\n      }\n\n      setAttributeNode(newAttr) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      removeAttributeNode(oldAttr) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByTagName(name) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM Level 2\n      getAttributeNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      setAttributeNS(namespaceURI, qualifiedName, value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      removeAttributeNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getAttributeNodeNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      setAttributeNodeNS(newAttr) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByTagNameNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      hasAttribute(name) {\n        return this.attribs.hasOwnProperty(name);\n      }\n\n      hasAttributeNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM Level 3\n      setIdAttribute(name, isId) {\n        if (this.attribs.hasOwnProperty(name)) {\n          return this.attribs[name].isId;\n        } else {\n          return isId;\n        }\n      }\n\n      setIdAttributeNS(namespaceURI, localName, isId) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      setIdAttributeNode(idAttr, isId) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM Level 4\n      getElementsByTagName(tagname) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByTagNameNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getElementsByClassName(classNames) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      isEqualNode(node) {\n        var i, j, ref;\n        if (!super.isEqualNode(node)) {\n          return false;\n        }\n        if (node.namespaceURI !== this.namespaceURI) {\n          return false;\n        }\n        if (node.prefix !== this.prefix) {\n          return false;\n        }\n        if (node.localName !== this.localName) {\n          return false;\n        }\n        if (node.attribs.length !== this.attribs.length) {\n          return false;\n        }\n        for (i = j = 0, ref = this.attribs.length - 1; (0 <= ref ? j <= ref : j >= ref); i = 0 <= ref ? ++j : --j) {\n          if (!this.attribs[i].isEqualNode(node.attribs[i])) {\n            return false;\n          }\n        }\n        return true;\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLElement.prototype, 'tagName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    // DOM level 4\n    Object.defineProperty(XMLElement.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'id', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'className', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'classList', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'attributes', {\n      get: function() {\n        if (!this.attributeMap || !this.attributeMap.nodes) {\n          this.attributeMap = new XMLNamedNodeMap(this.attribs);\n        }\n        return this.attributeMap;\n      }\n    });\n\n    return XMLElement;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js ***!
  \********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Represents a map of nodes accessed by a string key\n  var XMLNamedNodeMap;\n\n  module.exports = XMLNamedNodeMap = (function() {\n    class XMLNamedNodeMap {\n      // Initializes a new instance of `XMLNamedNodeMap`\n      // This is just a wrapper around an ordinary\n      // JS object.\n\n      // `nodes` the object containing nodes.\n      constructor(nodes) {\n        this.nodes = nodes;\n      }\n\n      // Creates and returns a deep clone of `this`\n\n      clone() {\n        // this class should not be cloned since it wraps\n        // around a given object. The calling function should check\n        // whether the wrapped object is null and supply a new object\n        // (from the clone).\n        return this.nodes = null;\n      }\n\n      // DOM Level 1\n      getNamedItem(name) {\n        return this.nodes[name];\n      }\n\n      setNamedItem(node) {\n        var oldNode;\n        oldNode = this.nodes[node.nodeName];\n        this.nodes[node.nodeName] = node;\n        return oldNode || null;\n      }\n\n      removeNamedItem(name) {\n        var oldNode;\n        oldNode = this.nodes[name];\n        delete this.nodes[name];\n        return oldNode || null;\n      }\n\n      item(index) {\n        return this.nodes[Object.keys(this.nodes)[index]] || null;\n      }\n\n      // DOM level 2 functions to be implemented later\n      getNamedItemNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\");\n      }\n\n      setNamedItemNS(node) {\n        throw new Error(\"This DOM method is not implemented.\");\n      }\n\n      removeNamedItemNS(namespaceURI, localName) {\n        throw new Error(\"This DOM method is not implemented.\");\n      }\n\n    };\n\n    \n    // DOM level 1\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function() {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n\n    return XMLNamedNodeMap;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNode.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var DocumentPosition, NodeType, XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNamedNodeMap, XMLNode, XMLNodeList, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject,\n    hasProp = {}.hasOwnProperty,\n    splice = [].splice;\n\n  ({isObject, isFunction, isEmpty, getValue} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  NodeType = null;\n\n  XMLNodeList = null;\n\n  XMLNamedNodeMap = null;\n\n  DocumentPosition = null;\n\n  // Represents a generic XMl element\n  module.exports = XMLNode = (function() {\n    class XMLNode {\n      // Initializes a new instance of `XMLNode`\n\n      // `parent` the parent node\n      constructor(parent1) {\n        this.parent = parent1;\n        if (this.parent) {\n          this.options = this.parent.options;\n          this.stringify = this.parent.stringify;\n        }\n        this.value = null;\n        this.children = [];\n        this.baseURI = null;\n        // first execution, load dependencies that are otherwise\n        // circular (so we can't load them at the top)\n        if (!XMLElement) {\n          XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n          XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n          XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n          XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n          XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n          XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n          XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n          XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n          XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n          NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n          XMLNodeList = __webpack_require__(/*! ./XMLNodeList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js\");\n          XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n          DocumentPosition = __webpack_require__(/*! ./DocumentPosition */ \"(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js\");\n        }\n      }\n\n      \n      // Sets the parent node of this node and its children recursively\n\n      // `parent` the parent node\n      setParent(parent) {\n        var child, j, len, ref1, results;\n        this.parent = parent;\n        if (parent) {\n          this.options = parent.options;\n          this.stringify = parent.stringify;\n        }\n        ref1 = this.children;\n        results = [];\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          results.push(child.setParent(this));\n        }\n        return results;\n      }\n\n      // Creates a child element node\n\n      // `name` node name or an object describing the XML tree\n      // `attributes` an object containing name/value pairs of attributes\n      // `text` element text\n      element(name, attributes, text) {\n        var childNode, item, j, k, key, lastChild, len, len1, val;\n        lastChild = null;\n        if (attributes === null && (text == null)) {\n          [attributes, text] = [{}, null];\n        }\n        if (attributes == null) {\n          attributes = {};\n        }\n        attributes = getValue(attributes);\n        // swap argument order: text <-> attributes\n        if (!isObject(attributes)) {\n          [text, attributes] = [attributes, text];\n        }\n        if (name != null) {\n          name = getValue(name);\n        }\n        // expand if array\n        if (Array.isArray(name)) {\n          for (j = 0, len = name.length; j < len; j++) {\n            item = name[j];\n            lastChild = this.element(item);\n          }\n        // evaluate if function\n        } else if (isFunction(name)) {\n          lastChild = this.element(name.apply());\n        // expand if object\n        } else if (isObject(name)) {\n          for (key in name) {\n            if (!hasProp.call(name, key)) continue;\n            val = name[key];\n            if (isFunction(val)) {\n              // evaluate if function\n              val = val.apply();\n            }\n            // assign attributes\n            if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n              lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n            // skip empty arrays\n            } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n              lastChild = this.dummy();\n            // empty objects produce one node\n            } else if (isObject(val) && isEmpty(val)) {\n              lastChild = this.element(key);\n            // skip null and undefined nodes\n            } else if (!this.options.keepNullNodes && (val == null)) {\n              lastChild = this.dummy();\n            \n            // expand list by creating child nodes\n            } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n              for (k = 0, len1 = val.length; k < len1; k++) {\n                item = val[k];\n                childNode = {};\n                childNode[key] = item;\n                lastChild = this.element(childNode);\n              }\n            \n            // expand child nodes under parent\n            } else if (isObject(val)) {\n              // if the key is #text expand child nodes under this node to support mixed content\n              if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n                lastChild = this.element(val);\n              } else {\n                lastChild = this.element(key);\n                lastChild.element(val);\n              }\n            } else {\n              \n              // text node\n              lastChild = this.element(key, val);\n            }\n          }\n        // skip null nodes\n        } else if (!this.options.keepNullNodes && text === null) {\n          lastChild = this.dummy();\n        } else {\n          // text node\n          if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n            lastChild = this.text(text);\n          // cdata node\n          } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n            lastChild = this.cdata(text);\n          // comment node\n          } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n            lastChild = this.comment(text);\n          // raw text node\n          } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n            lastChild = this.raw(text);\n          // processing instruction\n          } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n            lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n          } else {\n            // element node\n            lastChild = this.node(name, attributes, text);\n          }\n        }\n        if (lastChild == null) {\n          throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n        }\n        return lastChild;\n      }\n\n      // Creates a child element node before the current node\n\n      // `name` node name or an object describing the XML tree\n      // `attributes` an object containing name/value pairs of attributes\n      // `text` element text\n      insertBefore(name, attributes, text) {\n        var child, i, newChild, refChild, removed;\n        // DOM level 1\n        // insertBefore(newChild, refChild) inserts the child node newChild before refChild\n        if (name != null ? name.type : void 0) {\n          newChild = name;\n          refChild = attributes;\n          newChild.setParent(this);\n          if (refChild) {\n            // temporarily remove children starting *with* refChild\n            i = children.indexOf(refChild);\n            removed = children.splice(i);\n            \n            // add the new child\n            children.push(newChild);\n            \n            // add back removed children after new child\n            Array.prototype.push.apply(children, removed);\n          } else {\n            children.push(newChild);\n          }\n          return newChild;\n        } else {\n          if (this.isRoot) {\n            throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n          }\n          \n          // temporarily remove children starting *with* this\n          i = this.parent.children.indexOf(this);\n          removed = this.parent.children.splice(i);\n          \n          // add the new child\n          child = this.parent.element(name, attributes, text);\n          \n          // add back removed children after new child\n          Array.prototype.push.apply(this.parent.children, removed);\n          return child;\n        }\n      }\n\n      // Creates a child element node after the current node\n\n      // `name` node name or an object describing the XML tree\n      // `attributes` an object containing name/value pairs of attributes\n      // `text` element text\n      insertAfter(name, attributes, text) {\n        var child, i, removed;\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        \n        // temporarily remove children starting *after* this\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i + 1);\n        \n        // add the new child\n        child = this.parent.element(name, attributes, text);\n        \n        // add back removed children after new child\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n\n      // Deletes a child element node\n\n      remove() {\n        var i, ref1;\n        if (this.isRoot) {\n          throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n        }\n        i = this.parent.children.indexOf(this);\n        splice.apply(this.parent.children, [i, i - i + 1].concat(ref1 = [])), ref1;\n        return this.parent;\n      }\n\n      // Creates a node\n\n      // `name` name of the node\n      // `attributes` an object containing name/value pairs of attributes\n      // `text` element text\n      node(name, attributes, text) {\n        var child;\n        if (name != null) {\n          name = getValue(name);\n        }\n        attributes || (attributes = {});\n        attributes = getValue(attributes);\n        // swap argument order: text <-> attributes\n        if (!isObject(attributes)) {\n          [text, attributes] = [attributes, text];\n        }\n        child = new XMLElement(this, name, attributes);\n        if (text != null) {\n          child.text(text);\n        }\n        this.children.push(child);\n        return child;\n      }\n\n      // Creates a text node\n\n      // `value` element text\n      text(value) {\n        var child;\n        if (isObject(value)) {\n          this.element(value);\n        }\n        child = new XMLText(this, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a CDATA node\n\n      // `value` element text without CDATA delimiters\n      cdata(value) {\n        var child;\n        child = new XMLCData(this, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a comment node\n\n      // `value` comment text\n      comment(value) {\n        var child;\n        child = new XMLComment(this, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Creates a comment node before the current node\n\n      // `value` comment text\n      commentBefore(value) {\n        var child, i, removed;\n        // temporarily remove children starting *with* this\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        // add the new child\n        child = this.parent.comment(value);\n        // add back removed children after new child\n        Array.prototype.push.apply(this.parent.children, removed);\n        return this;\n      }\n\n      // Creates a comment node after the current node\n\n      // `value` comment text\n      commentAfter(value) {\n        var child, i, removed;\n        // temporarily remove children starting *after* this\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i + 1);\n        // add the new child\n        child = this.parent.comment(value);\n        // add back removed children after new child\n        Array.prototype.push.apply(this.parent.children, removed);\n        return this;\n      }\n\n      // Adds unescaped raw text\n\n      // `value` text\n      raw(value) {\n        var child;\n        child = new XMLRaw(this, value);\n        this.children.push(child);\n        return this;\n      }\n\n      // Adds a dummy node\n      dummy() {\n        var child;\n        child = new XMLDummy(this);\n        // Normally when a new node is created it is added to the child node collection.\n        // However, dummy nodes are never added to the XML tree. They are created while\n        // converting JS objects to XML nodes in order not to break the recursive function\n        // chain. They can be thought of as invisible nodes. They can be traversed through\n        // by using prev(), next(), up(), etc. functions but they do not exists in the tree.\n\n        // @children.push child\n        return child;\n      }\n\n      // Adds a processing instruction\n\n      // `target` instruction target\n      // `value` instruction value\n      instruction(target, value) {\n        var insTarget, insValue, instruction, j, len;\n        if (target != null) {\n          target = getValue(target);\n        }\n        if (value != null) {\n          value = getValue(value);\n        }\n        if (Array.isArray(target)) { // expand if array\n          for (j = 0, len = target.length; j < len; j++) {\n            insTarget = target[j];\n            this.instruction(insTarget);\n          }\n        } else if (isObject(target)) { // expand if object\n          for (insTarget in target) {\n            if (!hasProp.call(target, insTarget)) continue;\n            insValue = target[insTarget];\n            this.instruction(insTarget, insValue);\n          }\n        } else {\n          if (isFunction(value)) {\n            value = value.apply();\n          }\n          instruction = new XMLProcessingInstruction(this, target, value);\n          this.children.push(instruction);\n        }\n        return this;\n      }\n\n      // Creates a processing instruction node before the current node\n\n      // `target` instruction target\n      // `value` instruction value\n      instructionBefore(target, value) {\n        var child, i, removed;\n        // temporarily remove children starting *with* this\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        // add the new child\n        child = this.parent.instruction(target, value);\n        // add back removed children after new child\n        Array.prototype.push.apply(this.parent.children, removed);\n        return this;\n      }\n\n      // Creates a processing instruction node after the current node\n\n      // `target` instruction target\n      // `value` instruction value\n      instructionAfter(target, value) {\n        var child, i, removed;\n        // temporarily remove children starting *after* this\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i + 1);\n        // add the new child\n        child = this.parent.instruction(target, value);\n        // add back removed children after new child\n        Array.prototype.push.apply(this.parent.children, removed);\n        return this;\n      }\n\n      // Creates the xml declaration\n\n      // `version` A version number string, e.g. 1.0\n      // `encoding` Encoding declaration, e.g. UTF-8\n      // `standalone` standalone document declaration: true or false\n      declaration(version, encoding, standalone) {\n        var doc, xmldec;\n        doc = this.document();\n        xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n        // Replace XML declaration if exists, otherwise insert at top\n        if (doc.children.length === 0) {\n          doc.children.unshift(xmldec);\n        } else if (doc.children[0].type === NodeType.Declaration) {\n          doc.children[0] = xmldec;\n        } else {\n          doc.children.unshift(xmldec);\n        }\n        return doc.root() || doc;\n      }\n\n      // Creates the document type declaration\n\n      // `pubID` the public identifier of the external subset\n      // `sysID` the system identifier of the external subset\n      dtd(pubID, sysID) {\n        var child, doc, doctype, i, j, k, len, len1, ref1, ref2;\n        doc = this.document();\n        doctype = new XMLDocType(doc, pubID, sysID);\n        ref1 = doc.children;\n        // Replace DTD if exists\n        for (i = j = 0, len = ref1.length; j < len; i = ++j) {\n          child = ref1[i];\n          if (child.type === NodeType.DocType) {\n            doc.children[i] = doctype;\n            return doctype;\n          }\n        }\n        ref2 = doc.children;\n        // insert before root node if the root node exists\n        for (i = k = 0, len1 = ref2.length; k < len1; i = ++k) {\n          child = ref2[i];\n          if (child.isRoot) {\n            doc.children.splice(i, 0, doctype);\n            return doctype;\n          }\n        }\n        // otherwise append to end\n        doc.children.push(doctype);\n        return doctype;\n      }\n\n      // Gets the parent node\n      up() {\n        if (this.isRoot) {\n          throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n        }\n        return this.parent;\n      }\n\n      // Gets the root node\n      root() {\n        var node;\n        node = this;\n        while (node) {\n          if (node.type === NodeType.Document) {\n            return node.rootObject;\n          } else if (node.isRoot) {\n            return node;\n          } else {\n            node = node.parent;\n          }\n        }\n      }\n\n      // Gets the node representing the XML document\n      document() {\n        var node;\n        node = this;\n        while (node) {\n          if (node.type === NodeType.Document) {\n            return node;\n          } else {\n            node = node.parent;\n          }\n        }\n      }\n\n      // Ends the document and converts string\n      end(options) {\n        return this.document().end(options);\n      }\n\n      // Gets the previous node\n      prev() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        if (i < 1) {\n          throw new Error(\"Already at the first node. \" + this.debugInfo());\n        }\n        return this.parent.children[i - 1];\n      }\n\n      // Gets the next node\n      next() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        if (i === -1 || i === this.parent.children.length - 1) {\n          throw new Error(\"Already at the last node. \" + this.debugInfo());\n        }\n        return this.parent.children[i + 1];\n      }\n\n      // Imports cloned root from another XML document\n\n      // `doc` the XML document to insert nodes from\n      importDocument(doc) {\n        var child, clonedRoot, j, len, ref1;\n        clonedRoot = doc.root().clone();\n        clonedRoot.parent = this;\n        clonedRoot.isRoot = false;\n        this.children.push(clonedRoot);\n        // set properties if imported element becomes the root node\n        if (this.type === NodeType.Document) {\n          clonedRoot.isRoot = true;\n          clonedRoot.documentObject = this;\n          this.rootObject = clonedRoot;\n          // set dtd name\n          if (this.children) {\n            ref1 = this.children;\n            for (j = 0, len = ref1.length; j < len; j++) {\n              child = ref1[j];\n              if (child.type === NodeType.DocType) {\n                child.name = clonedRoot.name;\n                break;\n              }\n            }\n          }\n        }\n        return this;\n      }\n\n      \n      // Returns debug string for this node\n      debugInfo(name) {\n        var ref1, ref2;\n        name = name || this.name;\n        if ((name == null) && !((ref1 = this.parent) != null ? ref1.name : void 0)) {\n          return \"\";\n        } else if (name == null) {\n          return \"parent: <\" + this.parent.name + \">\";\n        } else if (!((ref2 = this.parent) != null ? ref2.name : void 0)) {\n          return \"node: <\" + name + \">\";\n        } else {\n          return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n        }\n      }\n\n      // Aliases\n      ele(name, attributes, text) {\n        return this.element(name, attributes, text);\n      }\n\n      nod(name, attributes, text) {\n        return this.node(name, attributes, text);\n      }\n\n      txt(value) {\n        return this.text(value);\n      }\n\n      dat(value) {\n        return this.cdata(value);\n      }\n\n      com(value) {\n        return this.comment(value);\n      }\n\n      ins(target, value) {\n        return this.instruction(target, value);\n      }\n\n      doc() {\n        return this.document();\n      }\n\n      dec(version, encoding, standalone) {\n        return this.declaration(version, encoding, standalone);\n      }\n\n      e(name, attributes, text) {\n        return this.element(name, attributes, text);\n      }\n\n      n(name, attributes, text) {\n        return this.node(name, attributes, text);\n      }\n\n      t(value) {\n        return this.text(value);\n      }\n\n      d(value) {\n        return this.cdata(value);\n      }\n\n      c(value) {\n        return this.comment(value);\n      }\n\n      r(value) {\n        return this.raw(value);\n      }\n\n      i(target, value) {\n        return this.instruction(target, value);\n      }\n\n      u() {\n        return this.up();\n      }\n\n      // can be deprecated in a future release\n      importXMLBuilder(doc) {\n        return this.importDocument(doc);\n      }\n\n      // Adds or modifies an attribute.\n\n      // `name` attribute name\n      // `value` attribute value\n      attribute(name, value) {\n        throw new Error(\"attribute() applies to element nodes only.\");\n      }\n\n      att(name, value) {\n        return this.attribute(name, value);\n      }\n\n      a(name, value) {\n        return this.attribute(name, value);\n      }\n\n      // Removes an attribute\n\n      // `name` attribute name\n      removeAttribute(name) {\n        throw new Error(\"attribute() applies to element nodes only.\");\n      }\n\n      // DOM level 1 functions to be implemented later\n      replaceChild(newChild, oldChild) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      removeChild(oldChild) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      appendChild(newChild) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      hasChildNodes() {\n        return this.children.length !== 0;\n      }\n\n      cloneNode(deep) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      normalize() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM level 2\n      isSupported(feature, version) {\n        return true;\n      }\n\n      hasAttributes() {\n        return this.attribs.length !== 0;\n      }\n\n      // DOM level 3 functions to be implemented later\n      compareDocumentPosition(other) {\n        var ref, res;\n        ref = this;\n        if (ref === other) {\n          return 0;\n        } else if (this.document() !== other.document()) {\n          res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n          if (Math.random() < 0.5) {\n            res |= DocumentPosition.Preceding;\n          } else {\n            res |= DocumentPosition.Following;\n          }\n          return res;\n        } else if (ref.isAncestor(other)) {\n          return DocumentPosition.Contains | DocumentPosition.Preceding;\n        } else if (ref.isDescendant(other)) {\n          return DocumentPosition.Contains | DocumentPosition.Following;\n        } else if (ref.isPreceding(other)) {\n          return DocumentPosition.Preceding;\n        } else {\n          return DocumentPosition.Following;\n        }\n      }\n\n      isSameNode(other) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      lookupPrefix(namespaceURI) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      isDefaultNamespace(namespaceURI) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      lookupNamespaceURI(prefix) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      isEqualNode(node) {\n        var i, j, ref1;\n        if (node.nodeType !== this.nodeType) {\n          return false;\n        }\n        if (node.children.length !== this.children.length) {\n          return false;\n        }\n        for (i = j = 0, ref1 = this.children.length - 1; (0 <= ref1 ? j <= ref1 : j >= ref1); i = 0 <= ref1 ? ++j : --j) {\n          if (!this.children[i].isEqualNode(node.children[i])) {\n            return false;\n          }\n        }\n        return true;\n      }\n\n      getFeature(feature, version) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      setUserData(key, data, handler) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      getUserData(key) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // Returns true if other is an inclusive descendant of node,\n      // and false otherwise.\n      contains(other) {\n        if (!other) {\n          return false;\n        }\n        return other === this || this.isDescendant(other);\n      }\n\n      // An object A is called a descendant of an object B, if either A is \n      // a child of B or A is a child of an object C that is a descendant of B.\n      isDescendant(node) {\n        var child, isDescendantChild, j, len, ref1;\n        ref1 = this.children;\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          if (node === child) {\n            return true;\n          }\n          isDescendantChild = child.isDescendant(node);\n          if (isDescendantChild) {\n            return true;\n          }\n        }\n        return false;\n      }\n\n      // An object A is called an ancestor of an object B if and only if\n      // B is a descendant of A.\n      isAncestor(node) {\n        return node.isDescendant(this);\n      }\n\n      // An object A is preceding an object B if A and B are in the \n      // same tree and A comes before B in tree order.\n      isPreceding(node) {\n        var nodePos, thisPos;\n        nodePos = this.treePosition(node);\n        thisPos = this.treePosition(this);\n        if (nodePos === -1 || thisPos === -1) {\n          return false;\n        } else {\n          return nodePos < thisPos;\n        }\n      }\n\n      // An object A is folllowing an object B if A and B are in the \n      // same tree and A comes after B in tree order.\n      isFollowing(node) {\n        var nodePos, thisPos;\n        nodePos = this.treePosition(node);\n        thisPos = this.treePosition(this);\n        if (nodePos === -1 || thisPos === -1) {\n          return false;\n        } else {\n          return nodePos > thisPos;\n        }\n      }\n\n      // Returns the preorder position of the given node in the tree, or -1\n      // if the node is not in the tree.\n      treePosition(node) {\n        var found, pos;\n        pos = 0;\n        found = false;\n        this.foreachTreeNode(this.document(), function(childNode) {\n          pos++;\n          if (!found && childNode === node) {\n            return found = true;\n          }\n        });\n        if (found) {\n          return pos;\n        } else {\n          return -1;\n        }\n      }\n\n      \n      // Depth-first preorder traversal through the XML tree\n      foreachTreeNode(node, func) {\n        var child, j, len, ref1, res;\n        node || (node = this.document());\n        ref1 = node.children;\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          if (res = func(child)) {\n            return res;\n          } else {\n            res = this.foreachTreeNode(child, func);\n            if (res) {\n              return res;\n            }\n          }\n        }\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function() {\n        return this.value;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function() {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function() {\n        return this.children[0] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function() {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function() {\n        return this.document() || null;\n      }\n    });\n\n    // DOM level 3\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function() {\n        var child, j, len, ref1, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref1 = this.children;\n          for (j = 0, len = ref1.length; j < len; j++) {\n            child = ref1[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function(value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    return XMLNode;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MTm9kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7O0FBRUEsSUFBSSx5Q0FBeUMsRUFBRSxtQkFBTyxDQUFDLGlFQUFXOztBQUVsRTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixtQkFBTyxDQUFDLHVFQUFjO0FBQzdDLHFCQUFxQixtQkFBTyxDQUFDLG1FQUFZO0FBQ3pDLHVCQUF1QixtQkFBTyxDQUFDLHVFQUFjO0FBQzdDLDJCQUEyQixtQkFBTyxDQUFDLCtFQUFrQjtBQUNyRCx1QkFBdUIsbUJBQU8sQ0FBQyx1RUFBYztBQUM3QyxtQkFBbUIsbUJBQU8sQ0FBQywrREFBVTtBQUNyQyxvQkFBb0IsbUJBQU8sQ0FBQyxpRUFBVztBQUN2QyxxQ0FBcUMsbUJBQU8sQ0FBQyxtR0FBNEI7QUFDekUscUJBQXFCLG1CQUFPLENBQUMsbUVBQVk7QUFDekMscUJBQXFCLG1CQUFPLENBQUMsbUVBQVk7QUFDekMsd0JBQXdCLG1CQUFPLENBQUMseUVBQWU7QUFDL0MsNEJBQTRCLG1CQUFPLENBQUMsaUZBQW1CO0FBQ3ZELDZCQUE2QixtQkFBTyxDQUFDLG1GQUFvQjtBQUN6RDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsU0FBUztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsU0FBUztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZCw2Q0FBNkMsVUFBVTtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckMsMkNBQTJDLFNBQVM7QUFDcEQ7QUFDQTtBQUNBO0FBQ0EsVUFBVSw2QkFBNkI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFVBQVU7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxxQ0FBcUM7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLFNBQVM7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxub2RlX21vZHVsZXNcXHhtbGJ1aWxkZXJcXGxpYlxcWE1MTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDIuNC4xXG4oZnVuY3Rpb24oKSB7XG4gIHZhciBEb2N1bWVudFBvc2l0aW9uLCBOb2RlVHlwZSwgWE1MQ0RhdGEsIFhNTENvbW1lbnQsIFhNTERlY2xhcmF0aW9uLCBYTUxEb2NUeXBlLCBYTUxEdW1teSwgWE1MRWxlbWVudCwgWE1MTmFtZWROb2RlTWFwLCBYTUxOb2RlLCBYTUxOb2RlTGlzdCwgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uLCBYTUxSYXcsIFhNTFRleHQsIGdldFZhbHVlLCBpc0VtcHR5LCBpc0Z1bmN0aW9uLCBpc09iamVjdCxcbiAgICBoYXNQcm9wID0ge30uaGFzT3duUHJvcGVydHksXG4gICAgc3BsaWNlID0gW10uc3BsaWNlO1xuXG4gICh7aXNPYmplY3QsIGlzRnVuY3Rpb24sIGlzRW1wdHksIGdldFZhbHVlfSA9IHJlcXVpcmUoJy4vVXRpbGl0eScpKTtcblxuICBYTUxFbGVtZW50ID0gbnVsbDtcblxuICBYTUxDRGF0YSA9IG51bGw7XG5cbiAgWE1MQ29tbWVudCA9IG51bGw7XG5cbiAgWE1MRGVjbGFyYXRpb24gPSBudWxsO1xuXG4gIFhNTERvY1R5cGUgPSBudWxsO1xuXG4gIFhNTFJhdyA9IG51bGw7XG5cbiAgWE1MVGV4dCA9IG51bGw7XG5cbiAgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uID0gbnVsbDtcblxuICBYTUxEdW1teSA9IG51bGw7XG5cbiAgTm9kZVR5cGUgPSBudWxsO1xuXG4gIFhNTE5vZGVMaXN0ID0gbnVsbDtcblxuICBYTUxOYW1lZE5vZGVNYXAgPSBudWxsO1xuXG4gIERvY3VtZW50UG9zaXRpb24gPSBudWxsO1xuXG4gIC8vIFJlcHJlc2VudHMgYSBnZW5lcmljIFhNbCBlbGVtZW50XG4gIG1vZHVsZS5leHBvcnRzID0gWE1MTm9kZSA9IChmdW5jdGlvbigpIHtcbiAgICBjbGFzcyBYTUxOb2RlIHtcbiAgICAgIC8vIEluaXRpYWxpemVzIGEgbmV3IGluc3RhbmNlIG9mIGBYTUxOb2RlYFxuXG4gICAgICAvLyBgcGFyZW50YCB0aGUgcGFyZW50IG5vZGVcbiAgICAgIGNvbnN0cnVjdG9yKHBhcmVudDEpIHtcbiAgICAgICAgdGhpcy5wYXJlbnQgPSBwYXJlbnQxO1xuICAgICAgICBpZiAodGhpcy5wYXJlbnQpIHtcbiAgICAgICAgICB0aGlzLm9wdGlvbnMgPSB0aGlzLnBhcmVudC5vcHRpb25zO1xuICAgICAgICAgIHRoaXMuc3RyaW5naWZ5ID0gdGhpcy5wYXJlbnQuc3RyaW5naWZ5O1xuICAgICAgICB9XG4gICAgICAgIHRoaXMudmFsdWUgPSBudWxsO1xuICAgICAgICB0aGlzLmNoaWxkcmVuID0gW107XG4gICAgICAgIHRoaXMuYmFzZVVSSSA9IG51bGw7XG4gICAgICAgIC8vIGZpcnN0IGV4ZWN1dGlvbiwgbG9hZCBkZXBlbmRlbmNpZXMgdGhhdCBhcmUgb3RoZXJ3aXNlXG4gICAgICAgIC8vIGNpcmN1bGFyIChzbyB3ZSBjYW4ndCBsb2FkIHRoZW0gYXQgdGhlIHRvcClcbiAgICAgICAgaWYgKCFYTUxFbGVtZW50KSB7XG4gICAgICAgICAgWE1MRWxlbWVudCA9IHJlcXVpcmUoJy4vWE1MRWxlbWVudCcpO1xuICAgICAgICAgIFhNTENEYXRhID0gcmVxdWlyZSgnLi9YTUxDRGF0YScpO1xuICAgICAgICAgIFhNTENvbW1lbnQgPSByZXF1aXJlKCcuL1hNTENvbW1lbnQnKTtcbiAgICAgICAgICBYTUxEZWNsYXJhdGlvbiA9IHJlcXVpcmUoJy4vWE1MRGVjbGFyYXRpb24nKTtcbiAgICAgICAgICBYTUxEb2NUeXBlID0gcmVxdWlyZSgnLi9YTUxEb2NUeXBlJyk7XG4gICAgICAgICAgWE1MUmF3ID0gcmVxdWlyZSgnLi9YTUxSYXcnKTtcbiAgICAgICAgICBYTUxUZXh0ID0gcmVxdWlyZSgnLi9YTUxUZXh0Jyk7XG4gICAgICAgICAgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uID0gcmVxdWlyZSgnLi9YTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24nKTtcbiAgICAgICAgICBYTUxEdW1teSA9IHJlcXVpcmUoJy4vWE1MRHVtbXknKTtcbiAgICAgICAgICBOb2RlVHlwZSA9IHJlcXVpcmUoJy4vTm9kZVR5cGUnKTtcbiAgICAgICAgICBYTUxOb2RlTGlzdCA9IHJlcXVpcmUoJy4vWE1MTm9kZUxpc3QnKTtcbiAgICAgICAgICBYTUxOYW1lZE5vZGVNYXAgPSByZXF1aXJlKCcuL1hNTE5hbWVkTm9kZU1hcCcpO1xuICAgICAgICAgIERvY3VtZW50UG9zaXRpb24gPSByZXF1aXJlKCcuL0RvY3VtZW50UG9zaXRpb24nKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBcbiAgICAgIC8vIFNldHMgdGhlIHBhcmVudCBub2RlIG9mIHRoaXMgbm9kZSBhbmQgaXRzIGNoaWxkcmVuIHJlY3Vyc2l2ZWx5XG5cbiAgICAgIC8vIGBwYXJlbnRgIHRoZSBwYXJlbnQgbm9kZVxuICAgICAgc2V0UGFyZW50KHBhcmVudCkge1xuICAgICAgICB2YXIgY2hpbGQsIGosIGxlbiwgcmVmMSwgcmVzdWx0cztcbiAgICAgICAgdGhpcy5wYXJlbnQgPSBwYXJlbnQ7XG4gICAgICAgIGlmIChwYXJlbnQpIHtcbiAgICAgICAgICB0aGlzLm9wdGlvbnMgPSBwYXJlbnQub3B0aW9ucztcbiAgICAgICAgICB0aGlzLnN0cmluZ2lmeSA9IHBhcmVudC5zdHJpbmdpZnk7XG4gICAgICAgIH1cbiAgICAgICAgcmVmMSA9IHRoaXMuY2hpbGRyZW47XG4gICAgICAgIHJlc3VsdHMgPSBbXTtcbiAgICAgICAgZm9yIChqID0gMCwgbGVuID0gcmVmMS5sZW5ndGg7IGogPCBsZW47IGorKykge1xuICAgICAgICAgIGNoaWxkID0gcmVmMVtqXTtcbiAgICAgICAgICByZXN1bHRzLnB1c2goY2hpbGQuc2V0UGFyZW50KHRoaXMpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0cztcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyBhIGNoaWxkIGVsZW1lbnQgbm9kZVxuXG4gICAgICAvLyBgbmFtZWAgbm9kZSBuYW1lIG9yIGFuIG9iamVjdCBkZXNjcmliaW5nIHRoZSBYTUwgdHJlZVxuICAgICAgLy8gYGF0dHJpYnV0ZXNgIGFuIG9iamVjdCBjb250YWluaW5nIG5hbWUvdmFsdWUgcGFpcnMgb2YgYXR0cmlidXRlc1xuICAgICAgLy8gYHRleHRgIGVsZW1lbnQgdGV4dFxuICAgICAgZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KSB7XG4gICAgICAgIHZhciBjaGlsZE5vZGUsIGl0ZW0sIGosIGssIGtleSwgbGFzdENoaWxkLCBsZW4sIGxlbjEsIHZhbDtcbiAgICAgICAgbGFzdENoaWxkID0gbnVsbDtcbiAgICAgICAgaWYgKGF0dHJpYnV0ZXMgPT09IG51bGwgJiYgKHRleHQgPT0gbnVsbCkpIHtcbiAgICAgICAgICBbYXR0cmlidXRlcywgdGV4dF0gPSBbe30sIG51bGxdO1xuICAgICAgICB9XG4gICAgICAgIGlmIChhdHRyaWJ1dGVzID09IG51bGwpIHtcbiAgICAgICAgICBhdHRyaWJ1dGVzID0ge307XG4gICAgICAgIH1cbiAgICAgICAgYXR0cmlidXRlcyA9IGdldFZhbHVlKGF0dHJpYnV0ZXMpO1xuICAgICAgICAvLyBzd2FwIGFyZ3VtZW50IG9yZGVyOiB0ZXh0IDwtPiBhdHRyaWJ1dGVzXG4gICAgICAgIGlmICghaXNPYmplY3QoYXR0cmlidXRlcykpIHtcbiAgICAgICAgICBbdGV4dCwgYXR0cmlidXRlc10gPSBbYXR0cmlidXRlcywgdGV4dF07XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5hbWUgIT0gbnVsbCkge1xuICAgICAgICAgIG5hbWUgPSBnZXRWYWx1ZShuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBleHBhbmQgaWYgYXJyYXlcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkobmFtZSkpIHtcbiAgICAgICAgICBmb3IgKGogPSAwLCBsZW4gPSBuYW1lLmxlbmd0aDsgaiA8IGxlbjsgaisrKSB7XG4gICAgICAgICAgICBpdGVtID0gbmFtZVtqXTtcbiAgICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMuZWxlbWVudChpdGVtKTtcbiAgICAgICAgICB9XG4gICAgICAgIC8vIGV2YWx1YXRlIGlmIGZ1bmN0aW9uXG4gICAgICAgIH0gZWxzZSBpZiAoaXNGdW5jdGlvbihuYW1lKSkge1xuICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMuZWxlbWVudChuYW1lLmFwcGx5KCkpO1xuICAgICAgICAvLyBleHBhbmQgaWYgb2JqZWN0XG4gICAgICAgIH0gZWxzZSBpZiAoaXNPYmplY3QobmFtZSkpIHtcbiAgICAgICAgICBmb3IgKGtleSBpbiBuYW1lKSB7XG4gICAgICAgICAgICBpZiAoIWhhc1Byb3AuY2FsbChuYW1lLCBrZXkpKSBjb250aW51ZTtcbiAgICAgICAgICAgIHZhbCA9IG5hbWVba2V5XTtcbiAgICAgICAgICAgIGlmIChpc0Z1bmN0aW9uKHZhbCkpIHtcbiAgICAgICAgICAgICAgLy8gZXZhbHVhdGUgaWYgZnVuY3Rpb25cbiAgICAgICAgICAgICAgdmFsID0gdmFsLmFwcGx5KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBhc3NpZ24gYXR0cmlidXRlc1xuICAgICAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0QXR0S2V5ICYmIGtleS5pbmRleE9mKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRBdHRLZXkpID09PSAwKSB7XG4gICAgICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMuYXR0cmlidXRlKGtleS5zdWJzdHIodGhpcy5zdHJpbmdpZnkuY29udmVydEF0dEtleS5sZW5ndGgpLCB2YWwpO1xuICAgICAgICAgICAgLy8gc2tpcCBlbXB0eSBhcnJheXNcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIXRoaXMub3B0aW9ucy5zZXBhcmF0ZUFycmF5SXRlbXMgJiYgQXJyYXkuaXNBcnJheSh2YWwpICYmIGlzRW1wdHkodmFsKSkge1xuICAgICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmR1bW15KCk7XG4gICAgICAgICAgICAvLyBlbXB0eSBvYmplY3RzIHByb2R1Y2Ugb25lIG5vZGVcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNPYmplY3QodmFsKSAmJiBpc0VtcHR5KHZhbCkpIHtcbiAgICAgICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5lbGVtZW50KGtleSk7XG4gICAgICAgICAgICAvLyBza2lwIG51bGwgYW5kIHVuZGVmaW5lZCBub2Rlc1xuICAgICAgICAgICAgfSBlbHNlIGlmICghdGhpcy5vcHRpb25zLmtlZXBOdWxsTm9kZXMgJiYgKHZhbCA9PSBudWxsKSkge1xuICAgICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmR1bW15KCk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIGV4cGFuZCBsaXN0IGJ5IGNyZWF0aW5nIGNoaWxkIG5vZGVzXG4gICAgICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuc2VwYXJhdGVBcnJheUl0ZW1zICYmIEFycmF5LmlzQXJyYXkodmFsKSkge1xuICAgICAgICAgICAgICBmb3IgKGsgPSAwLCBsZW4xID0gdmFsLmxlbmd0aDsgayA8IGxlbjE7IGsrKykge1xuICAgICAgICAgICAgICAgIGl0ZW0gPSB2YWxba107XG4gICAgICAgICAgICAgICAgY2hpbGROb2RlID0ge307XG4gICAgICAgICAgICAgICAgY2hpbGROb2RlW2tleV0gPSBpdGVtO1xuICAgICAgICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMuZWxlbWVudChjaGlsZE5vZGUpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIGV4cGFuZCBjaGlsZCBub2RlcyB1bmRlciBwYXJlbnRcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNPYmplY3QodmFsKSkge1xuICAgICAgICAgICAgICAvLyBpZiB0aGUga2V5IGlzICN0ZXh0IGV4cGFuZCBjaGlsZCBub2RlcyB1bmRlciB0aGlzIG5vZGUgdG8gc3VwcG9ydCBtaXhlZCBjb250ZW50XG4gICAgICAgICAgICAgIGlmICghdGhpcy5vcHRpb25zLmlnbm9yZURlY29yYXRvcnMgJiYgdGhpcy5zdHJpbmdpZnkuY29udmVydFRleHRLZXkgJiYga2V5LmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydFRleHRLZXkpID09PSAwKSB7XG4gICAgICAgICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5lbGVtZW50KHZhbCk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5lbGVtZW50KGtleSk7XG4gICAgICAgICAgICAgICAgbGFzdENoaWxkLmVsZW1lbnQodmFsKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIC8vIHRleHQgbm9kZVxuICAgICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmVsZW1lbnQoa2V5LCB2YWwpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgLy8gc2tpcCBudWxsIG5vZGVzXG4gICAgICAgIH0gZWxzZSBpZiAoIXRoaXMub3B0aW9ucy5rZWVwTnVsbE5vZGVzICYmIHRleHQgPT09IG51bGwpIHtcbiAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmR1bW15KCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gdGV4dCBub2RlXG4gICAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0VGV4dEtleSAmJiBuYW1lLmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydFRleHRLZXkpID09PSAwKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLnRleHQodGV4dCk7XG4gICAgICAgICAgLy8gY2RhdGEgbm9kZVxuICAgICAgICAgIH0gZWxzZSBpZiAoIXRoaXMub3B0aW9ucy5pZ25vcmVEZWNvcmF0b3JzICYmIHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRDRGF0YUtleSAmJiBuYW1lLmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydENEYXRhS2V5KSA9PT0gMCkge1xuICAgICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5jZGF0YSh0ZXh0KTtcbiAgICAgICAgICAvLyBjb21tZW50IG5vZGVcbiAgICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0Q29tbWVudEtleSAmJiBuYW1lLmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydENvbW1lbnRLZXkpID09PSAwKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmNvbW1lbnQodGV4dCk7XG4gICAgICAgICAgLy8gcmF3IHRleHQgbm9kZVxuICAgICAgICAgIH0gZWxzZSBpZiAoIXRoaXMub3B0aW9ucy5pZ25vcmVEZWNvcmF0b3JzICYmIHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRSYXdLZXkgJiYgbmFtZS5pbmRleE9mKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRSYXdLZXkpID09PSAwKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLnJhdyh0ZXh0KTtcbiAgICAgICAgICAvLyBwcm9jZXNzaW5nIGluc3RydWN0aW9uXG4gICAgICAgICAgfSBlbHNlIGlmICghdGhpcy5vcHRpb25zLmlnbm9yZURlY29yYXRvcnMgJiYgdGhpcy5zdHJpbmdpZnkuY29udmVydFBJS2V5ICYmIG5hbWUuaW5kZXhPZih0aGlzLnN0cmluZ2lmeS5jb252ZXJ0UElLZXkpID09PSAwKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmluc3RydWN0aW9uKG5hbWUuc3Vic3RyKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRQSUtleS5sZW5ndGgpLCB0ZXh0KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gZWxlbWVudCBub2RlXG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLm5vZGUobmFtZSwgYXR0cmlidXRlcywgdGV4dCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChsYXN0Q2hpbGQgPT0gbnVsbCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNvdWxkIG5vdCBjcmVhdGUgYW55IGVsZW1lbnRzIHdpdGg6IFwiICsgbmFtZSArIFwiLiBcIiArIHRoaXMuZGVidWdJbmZvKCkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBsYXN0Q2hpbGQ7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZXMgYSBjaGlsZCBlbGVtZW50IG5vZGUgYmVmb3JlIHRoZSBjdXJyZW50IG5vZGVcblxuICAgICAgLy8gYG5hbWVgIG5vZGUgbmFtZSBvciBhbiBvYmplY3QgZGVzY3JpYmluZyB0aGUgWE1MIHRyZWVcbiAgICAgIC8vIGBhdHRyaWJ1dGVzYCBhbiBvYmplY3QgY29udGFpbmluZyBuYW1lL3ZhbHVlIHBhaXJzIG9mIGF0dHJpYnV0ZXNcbiAgICAgIC8vIGB0ZXh0YCBlbGVtZW50IHRleHRcbiAgICAgIGluc2VydEJlZm9yZShuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KSB7XG4gICAgICAgIHZhciBjaGlsZCwgaSwgbmV3Q2hpbGQsIHJlZkNoaWxkLCByZW1vdmVkO1xuICAgICAgICAvLyBET00gbGV2ZWwgMVxuICAgICAgICAvLyBpbnNlcnRCZWZvcmUobmV3Q2hpbGQsIHJlZkNoaWxkKSBpbnNlcnRzIHRoZSBjaGlsZCBub2RlIG5ld0NoaWxkIGJlZm9yZSByZWZDaGlsZFxuICAgICAgICBpZiAobmFtZSAhPSBudWxsID8gbmFtZS50eXBlIDogdm9pZCAwKSB7XG4gICAgICAgICAgbmV3Q2hpbGQgPSBuYW1lO1xuICAgICAgICAgIHJlZkNoaWxkID0gYXR0cmlidXRlcztcbiAgICAgICAgICBuZXdDaGlsZC5zZXRQYXJlbnQodGhpcyk7XG4gICAgICAgICAgaWYgKHJlZkNoaWxkKSB7XG4gICAgICAgICAgICAvLyB0ZW1wb3JhcmlseSByZW1vdmUgY2hpbGRyZW4gc3RhcnRpbmcgKndpdGgqIHJlZkNoaWxkXG4gICAgICAgICAgICBpID0gY2hpbGRyZW4uaW5kZXhPZihyZWZDaGlsZCk7XG4gICAgICAgICAgICByZW1vdmVkID0gY2hpbGRyZW4uc3BsaWNlKGkpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBhZGQgdGhlIG5ldyBjaGlsZFxuICAgICAgICAgICAgY2hpbGRyZW4ucHVzaChuZXdDaGlsZCk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIGFkZCBiYWNrIHJlbW92ZWQgY2hpbGRyZW4gYWZ0ZXIgbmV3IGNoaWxkXG4gICAgICAgICAgICBBcnJheS5wcm90b3R5cGUucHVzaC5hcHBseShjaGlsZHJlbiwgcmVtb3ZlZCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNoaWxkcmVuLnB1c2gobmV3Q2hpbGQpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gbmV3Q2hpbGQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKHRoaXMuaXNSb290KSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgaW5zZXJ0IGVsZW1lbnRzIGF0IHJvb3QgbGV2ZWwuIFwiICsgdGhpcy5kZWJ1Z0luZm8obmFtZSkpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBcbiAgICAgICAgICAvLyB0ZW1wb3JhcmlseSByZW1vdmUgY2hpbGRyZW4gc3RhcnRpbmcgKndpdGgqIHRoaXNcbiAgICAgICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIGFkZCB0aGUgbmV3IGNoaWxkXG4gICAgICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5lbGVtZW50KG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIGFkZCBiYWNrIHJlbW92ZWQgY2hpbGRyZW4gYWZ0ZXIgbmV3IGNoaWxkXG4gICAgICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgICAgIHJldHVybiBjaGlsZDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGVzIGEgY2hpbGQgZWxlbWVudCBub2RlIGFmdGVyIHRoZSBjdXJyZW50IG5vZGVcblxuICAgICAgLy8gYG5hbWVgIG5vZGUgbmFtZSBvciBhbiBvYmplY3QgZGVzY3JpYmluZyB0aGUgWE1MIHRyZWVcbiAgICAgIC8vIGBhdHRyaWJ1dGVzYCBhbiBvYmplY3QgY29udGFpbmluZyBuYW1lL3ZhbHVlIHBhaXJzIG9mIGF0dHJpYnV0ZXNcbiAgICAgIC8vIGB0ZXh0YCBlbGVtZW50IHRleHRcbiAgICAgIGluc2VydEFmdGVyKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgICAgdmFyIGNoaWxkLCBpLCByZW1vdmVkO1xuICAgICAgICBpZiAodGhpcy5pc1Jvb3QpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgaW5zZXJ0IGVsZW1lbnRzIGF0IHJvb3QgbGV2ZWwuIFwiICsgdGhpcy5kZWJ1Z0luZm8obmFtZSkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyB0ZW1wb3JhcmlseSByZW1vdmUgY2hpbGRyZW4gc3RhcnRpbmcgKmFmdGVyKiB0aGlzXG4gICAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkgKyAxKTtcbiAgICAgICAgXG4gICAgICAgIC8vIGFkZCB0aGUgbmV3IGNoaWxkXG4gICAgICAgIGNoaWxkID0gdGhpcy5wYXJlbnQuZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICAgICAgXG4gICAgICAgIC8vIGFkZCBiYWNrIHJlbW92ZWQgY2hpbGRyZW4gYWZ0ZXIgbmV3IGNoaWxkXG4gICAgICAgIEFycmF5LnByb3RvdHlwZS5wdXNoLmFwcGx5KHRoaXMucGFyZW50LmNoaWxkcmVuLCByZW1vdmVkKTtcbiAgICAgICAgcmV0dXJuIGNoaWxkO1xuICAgICAgfVxuXG4gICAgICAvLyBEZWxldGVzIGEgY2hpbGQgZWxlbWVudCBub2RlXG5cbiAgICAgIHJlbW92ZSgpIHtcbiAgICAgICAgdmFyIGksIHJlZjE7XG4gICAgICAgIGlmICh0aGlzLmlzUm9vdCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNhbm5vdCByZW1vdmUgdGhlIHJvb3QgZWxlbWVudC4gXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgICAgfVxuICAgICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgICAgc3BsaWNlLmFwcGx5KHRoaXMucGFyZW50LmNoaWxkcmVuLCBbaSwgaSAtIGkgKyAxXS5jb25jYXQocmVmMSA9IFtdKSksIHJlZjE7XG4gICAgICAgIHJldHVybiB0aGlzLnBhcmVudDtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyBhIG5vZGVcblxuICAgICAgLy8gYG5hbWVgIG5hbWUgb2YgdGhlIG5vZGVcbiAgICAgIC8vIGBhdHRyaWJ1dGVzYCBhbiBvYmplY3QgY29udGFpbmluZyBuYW1lL3ZhbHVlIHBhaXJzIG9mIGF0dHJpYnV0ZXNcbiAgICAgIC8vIGB0ZXh0YCBlbGVtZW50IHRleHRcbiAgICAgIG5vZGUobmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgICB2YXIgY2hpbGQ7XG4gICAgICAgIGlmIChuYW1lICE9IG51bGwpIHtcbiAgICAgICAgICBuYW1lID0gZ2V0VmFsdWUobmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgYXR0cmlidXRlcyB8fCAoYXR0cmlidXRlcyA9IHt9KTtcbiAgICAgICAgYXR0cmlidXRlcyA9IGdldFZhbHVlKGF0dHJpYnV0ZXMpO1xuICAgICAgICAvLyBzd2FwIGFyZ3VtZW50IG9yZGVyOiB0ZXh0IDwtPiBhdHRyaWJ1dGVzXG4gICAgICAgIGlmICghaXNPYmplY3QoYXR0cmlidXRlcykpIHtcbiAgICAgICAgICBbdGV4dCwgYXR0cmlidXRlc10gPSBbYXR0cmlidXRlcywgdGV4dF07XG4gICAgICAgIH1cbiAgICAgICAgY2hpbGQgPSBuZXcgWE1MRWxlbWVudCh0aGlzLCBuYW1lLCBhdHRyaWJ1dGVzKTtcbiAgICAgICAgaWYgKHRleHQgIT0gbnVsbCkge1xuICAgICAgICAgIGNoaWxkLnRleHQodGV4dCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgICAgcmV0dXJuIGNoaWxkO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGVzIGEgdGV4dCBub2RlXG5cbiAgICAgIC8vIGB2YWx1ZWAgZWxlbWVudCB0ZXh0XG4gICAgICB0ZXh0KHZhbHVlKSB7XG4gICAgICAgIHZhciBjaGlsZDtcbiAgICAgICAgaWYgKGlzT2JqZWN0KHZhbHVlKSkge1xuICAgICAgICAgIHRoaXMuZWxlbWVudCh2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgY2hpbGQgPSBuZXcgWE1MVGV4dCh0aGlzLCB2YWx1ZSk7XG4gICAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGVzIGEgQ0RBVEEgbm9kZVxuXG4gICAgICAvLyBgdmFsdWVgIGVsZW1lbnQgdGV4dCB3aXRob3V0IENEQVRBIGRlbGltaXRlcnNcbiAgICAgIGNkYXRhKHZhbHVlKSB7XG4gICAgICAgIHZhciBjaGlsZDtcbiAgICAgICAgY2hpbGQgPSBuZXcgWE1MQ0RhdGEodGhpcywgdmFsdWUpO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyBhIGNvbW1lbnQgbm9kZVxuXG4gICAgICAvLyBgdmFsdWVgIGNvbW1lbnQgdGV4dFxuICAgICAgY29tbWVudCh2YWx1ZSkge1xuICAgICAgICB2YXIgY2hpbGQ7XG4gICAgICAgIGNoaWxkID0gbmV3IFhNTENvbW1lbnQodGhpcywgdmFsdWUpO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyBhIGNvbW1lbnQgbm9kZSBiZWZvcmUgdGhlIGN1cnJlbnQgbm9kZVxuXG4gICAgICAvLyBgdmFsdWVgIGNvbW1lbnQgdGV4dFxuICAgICAgY29tbWVudEJlZm9yZSh2YWx1ZSkge1xuICAgICAgICB2YXIgY2hpbGQsIGksIHJlbW92ZWQ7XG4gICAgICAgIC8vIHRlbXBvcmFyaWx5IHJlbW92ZSBjaGlsZHJlbiBzdGFydGluZyAqd2l0aCogdGhpc1xuICAgICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgICAgcmVtb3ZlZCA9IHRoaXMucGFyZW50LmNoaWxkcmVuLnNwbGljZShpKTtcbiAgICAgICAgLy8gYWRkIHRoZSBuZXcgY2hpbGRcbiAgICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5jb21tZW50KHZhbHVlKTtcbiAgICAgICAgLy8gYWRkIGJhY2sgcmVtb3ZlZCBjaGlsZHJlbiBhZnRlciBuZXcgY2hpbGRcbiAgICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyBhIGNvbW1lbnQgbm9kZSBhZnRlciB0aGUgY3VycmVudCBub2RlXG5cbiAgICAgIC8vIGB2YWx1ZWAgY29tbWVudCB0ZXh0XG4gICAgICBjb21tZW50QWZ0ZXIodmFsdWUpIHtcbiAgICAgICAgdmFyIGNoaWxkLCBpLCByZW1vdmVkO1xuICAgICAgICAvLyB0ZW1wb3JhcmlseSByZW1vdmUgY2hpbGRyZW4gc3RhcnRpbmcgKmFmdGVyKiB0aGlzXG4gICAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkgKyAxKTtcbiAgICAgICAgLy8gYWRkIHRoZSBuZXcgY2hpbGRcbiAgICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5jb21tZW50KHZhbHVlKTtcbiAgICAgICAgLy8gYWRkIGJhY2sgcmVtb3ZlZCBjaGlsZHJlbiBhZnRlciBuZXcgY2hpbGRcbiAgICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQWRkcyB1bmVzY2FwZWQgcmF3IHRleHRcblxuICAgICAgLy8gYHZhbHVlYCB0ZXh0XG4gICAgICByYXcodmFsdWUpIHtcbiAgICAgICAgdmFyIGNoaWxkO1xuICAgICAgICBjaGlsZCA9IG5ldyBYTUxSYXcodGhpcywgdmFsdWUpO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQWRkcyBhIGR1bW15IG5vZGVcbiAgICAgIGR1bW15KCkge1xuICAgICAgICB2YXIgY2hpbGQ7XG4gICAgICAgIGNoaWxkID0gbmV3IFhNTER1bW15KHRoaXMpO1xuICAgICAgICAvLyBOb3JtYWxseSB3aGVuIGEgbmV3IG5vZGUgaXMgY3JlYXRlZCBpdCBpcyBhZGRlZCB0byB0aGUgY2hpbGQgbm9kZSBjb2xsZWN0aW9uLlxuICAgICAgICAvLyBIb3dldmVyLCBkdW1teSBub2RlcyBhcmUgbmV2ZXIgYWRkZWQgdG8gdGhlIFhNTCB0cmVlLiBUaGV5IGFyZSBjcmVhdGVkIHdoaWxlXG4gICAgICAgIC8vIGNvbnZlcnRpbmcgSlMgb2JqZWN0cyB0byBYTUwgbm9kZXMgaW4gb3JkZXIgbm90IHRvIGJyZWFrIHRoZSByZWN1cnNpdmUgZnVuY3Rpb25cbiAgICAgICAgLy8gY2hhaW4uIFRoZXkgY2FuIGJlIHRob3VnaHQgb2YgYXMgaW52aXNpYmxlIG5vZGVzLiBUaGV5IGNhbiBiZSB0cmF2ZXJzZWQgdGhyb3VnaFxuICAgICAgICAvLyBieSB1c2luZyBwcmV2KCksIG5leHQoKSwgdXAoKSwgZXRjLiBmdW5jdGlvbnMgYnV0IHRoZXkgZG8gbm90IGV4aXN0cyBpbiB0aGUgdHJlZS5cblxuICAgICAgICAvLyBAY2hpbGRyZW4ucHVzaCBjaGlsZFxuICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZHMgYSBwcm9jZXNzaW5nIGluc3RydWN0aW9uXG5cbiAgICAgIC8vIGB0YXJnZXRgIGluc3RydWN0aW9uIHRhcmdldFxuICAgICAgLy8gYHZhbHVlYCBpbnN0cnVjdGlvbiB2YWx1ZVxuICAgICAgaW5zdHJ1Y3Rpb24odGFyZ2V0LCB2YWx1ZSkge1xuICAgICAgICB2YXIgaW5zVGFyZ2V0LCBpbnNWYWx1ZSwgaW5zdHJ1Y3Rpb24sIGosIGxlbjtcbiAgICAgICAgaWYgKHRhcmdldCAhPSBudWxsKSB7XG4gICAgICAgICAgdGFyZ2V0ID0gZ2V0VmFsdWUodGFyZ2V0KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodmFsdWUgIT0gbnVsbCkge1xuICAgICAgICAgIHZhbHVlID0gZ2V0VmFsdWUodmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHRhcmdldCkpIHsgLy8gZXhwYW5kIGlmIGFycmF5XG4gICAgICAgICAgZm9yIChqID0gMCwgbGVuID0gdGFyZ2V0Lmxlbmd0aDsgaiA8IGxlbjsgaisrKSB7XG4gICAgICAgICAgICBpbnNUYXJnZXQgPSB0YXJnZXRbal07XG4gICAgICAgICAgICB0aGlzLmluc3RydWN0aW9uKGluc1RhcmdldCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKGlzT2JqZWN0KHRhcmdldCkpIHsgLy8gZXhwYW5kIGlmIG9iamVjdFxuICAgICAgICAgIGZvciAoaW5zVGFyZ2V0IGluIHRhcmdldCkge1xuICAgICAgICAgICAgaWYgKCFoYXNQcm9wLmNhbGwodGFyZ2V0LCBpbnNUYXJnZXQpKSBjb250aW51ZTtcbiAgICAgICAgICAgIGluc1ZhbHVlID0gdGFyZ2V0W2luc1RhcmdldF07XG4gICAgICAgICAgICB0aGlzLmluc3RydWN0aW9uKGluc1RhcmdldCwgaW5zVmFsdWUpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWx1ZSkpIHtcbiAgICAgICAgICAgIHZhbHVlID0gdmFsdWUuYXBwbHkoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaW5zdHJ1Y3Rpb24gPSBuZXcgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uKHRoaXMsIHRhcmdldCwgdmFsdWUpO1xuICAgICAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChpbnN0cnVjdGlvbik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZXMgYSBwcm9jZXNzaW5nIGluc3RydWN0aW9uIG5vZGUgYmVmb3JlIHRoZSBjdXJyZW50IG5vZGVcblxuICAgICAgLy8gYHRhcmdldGAgaW5zdHJ1Y3Rpb24gdGFyZ2V0XG4gICAgICAvLyBgdmFsdWVgIGluc3RydWN0aW9uIHZhbHVlXG4gICAgICBpbnN0cnVjdGlvbkJlZm9yZSh0YXJnZXQsIHZhbHVlKSB7XG4gICAgICAgIHZhciBjaGlsZCwgaSwgcmVtb3ZlZDtcbiAgICAgICAgLy8gdGVtcG9yYXJpbHkgcmVtb3ZlIGNoaWxkcmVuIHN0YXJ0aW5nICp3aXRoKiB0aGlzXG4gICAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkpO1xuICAgICAgICAvLyBhZGQgdGhlIG5ldyBjaGlsZFxuICAgICAgICBjaGlsZCA9IHRoaXMucGFyZW50Lmluc3RydWN0aW9uKHRhcmdldCwgdmFsdWUpO1xuICAgICAgICAvLyBhZGQgYmFjayByZW1vdmVkIGNoaWxkcmVuIGFmdGVyIG5ldyBjaGlsZFxuICAgICAgICBBcnJheS5wcm90b3R5cGUucHVzaC5hcHBseSh0aGlzLnBhcmVudC5jaGlsZHJlbiwgcmVtb3ZlZCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGVzIGEgcHJvY2Vzc2luZyBpbnN0cnVjdGlvbiBub2RlIGFmdGVyIHRoZSBjdXJyZW50IG5vZGVcblxuICAgICAgLy8gYHRhcmdldGAgaW5zdHJ1Y3Rpb24gdGFyZ2V0XG4gICAgICAvLyBgdmFsdWVgIGluc3RydWN0aW9uIHZhbHVlXG4gICAgICBpbnN0cnVjdGlvbkFmdGVyKHRhcmdldCwgdmFsdWUpIHtcbiAgICAgICAgdmFyIGNoaWxkLCBpLCByZW1vdmVkO1xuICAgICAgICAvLyB0ZW1wb3JhcmlseSByZW1vdmUgY2hpbGRyZW4gc3RhcnRpbmcgKmFmdGVyKiB0aGlzXG4gICAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkgKyAxKTtcbiAgICAgICAgLy8gYWRkIHRoZSBuZXcgY2hpbGRcbiAgICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5pbnN0cnVjdGlvbih0YXJnZXQsIHZhbHVlKTtcbiAgICAgICAgLy8gYWRkIGJhY2sgcmVtb3ZlZCBjaGlsZHJlbiBhZnRlciBuZXcgY2hpbGRcbiAgICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlcyB0aGUgeG1sIGRlY2xhcmF0aW9uXG5cbiAgICAgIC8vIGB2ZXJzaW9uYCBBIHZlcnNpb24gbnVtYmVyIHN0cmluZywgZS5nLiAxLjBcbiAgICAgIC8vIGBlbmNvZGluZ2AgRW5jb2RpbmcgZGVjbGFyYXRpb24sIGUuZy4gVVRGLThcbiAgICAgIC8vIGBzdGFuZGFsb25lYCBzdGFuZGFsb25lIGRvY3VtZW50IGRlY2xhcmF0aW9uOiB0cnVlIG9yIGZhbHNlXG4gICAgICBkZWNsYXJhdGlvbih2ZXJzaW9uLCBlbmNvZGluZywgc3RhbmRhbG9uZSkge1xuICAgICAgICB2YXIgZG9jLCB4bWxkZWM7XG4gICAgICAgIGRvYyA9IHRoaXMuZG9jdW1lbnQoKTtcbiAgICAgICAgeG1sZGVjID0gbmV3IFhNTERlY2xhcmF0aW9uKGRvYywgdmVyc2lvbiwgZW5jb2RpbmcsIHN0YW5kYWxvbmUpO1xuICAgICAgICAvLyBSZXBsYWNlIFhNTCBkZWNsYXJhdGlvbiBpZiBleGlzdHMsIG90aGVyd2lzZSBpbnNlcnQgYXQgdG9wXG4gICAgICAgIGlmIChkb2MuY2hpbGRyZW4ubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgZG9jLmNoaWxkcmVuLnVuc2hpZnQoeG1sZGVjKTtcbiAgICAgICAgfSBlbHNlIGlmIChkb2MuY2hpbGRyZW5bMF0udHlwZSA9PT0gTm9kZVR5cGUuRGVjbGFyYXRpb24pIHtcbiAgICAgICAgICBkb2MuY2hpbGRyZW5bMF0gPSB4bWxkZWM7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZG9jLmNoaWxkcmVuLnVuc2hpZnQoeG1sZGVjKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZG9jLnJvb3QoKSB8fCBkb2M7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZXMgdGhlIGRvY3VtZW50IHR5cGUgZGVjbGFyYXRpb25cblxuICAgICAgLy8gYHB1YklEYCB0aGUgcHVibGljIGlkZW50aWZpZXIgb2YgdGhlIGV4dGVybmFsIHN1YnNldFxuICAgICAgLy8gYHN5c0lEYCB0aGUgc3lzdGVtIGlkZW50aWZpZXIgb2YgdGhlIGV4dGVybmFsIHN1YnNldFxuICAgICAgZHRkKHB1YklELCBzeXNJRCkge1xuICAgICAgICB2YXIgY2hpbGQsIGRvYywgZG9jdHlwZSwgaSwgaiwgaywgbGVuLCBsZW4xLCByZWYxLCByZWYyO1xuICAgICAgICBkb2MgPSB0aGlzLmRvY3VtZW50KCk7XG4gICAgICAgIGRvY3R5cGUgPSBuZXcgWE1MRG9jVHlwZShkb2MsIHB1YklELCBzeXNJRCk7XG4gICAgICAgIHJlZjEgPSBkb2MuY2hpbGRyZW47XG4gICAgICAgIC8vIFJlcGxhY2UgRFREIGlmIGV4aXN0c1xuICAgICAgICBmb3IgKGkgPSBqID0gMCwgbGVuID0gcmVmMS5sZW5ndGg7IGogPCBsZW47IGkgPSArK2opIHtcbiAgICAgICAgICBjaGlsZCA9IHJlZjFbaV07XG4gICAgICAgICAgaWYgKGNoaWxkLnR5cGUgPT09IE5vZGVUeXBlLkRvY1R5cGUpIHtcbiAgICAgICAgICAgIGRvYy5jaGlsZHJlbltpXSA9IGRvY3R5cGU7XG4gICAgICAgICAgICByZXR1cm4gZG9jdHlwZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmVmMiA9IGRvYy5jaGlsZHJlbjtcbiAgICAgICAgLy8gaW5zZXJ0IGJlZm9yZSByb290IG5vZGUgaWYgdGhlIHJvb3Qgbm9kZSBleGlzdHNcbiAgICAgICAgZm9yIChpID0gayA9IDAsIGxlbjEgPSByZWYyLmxlbmd0aDsgayA8IGxlbjE7IGkgPSArK2spIHtcbiAgICAgICAgICBjaGlsZCA9IHJlZjJbaV07XG4gICAgICAgICAgaWYgKGNoaWxkLmlzUm9vdCkge1xuICAgICAgICAgICAgZG9jLmNoaWxkcmVuLnNwbGljZShpLCAwLCBkb2N0eXBlKTtcbiAgICAgICAgICAgIHJldHVybiBkb2N0eXBlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyBvdGhlcndpc2UgYXBwZW5kIHRvIGVuZFxuICAgICAgICBkb2MuY2hpbGRyZW4ucHVzaChkb2N0eXBlKTtcbiAgICAgICAgcmV0dXJuIGRvY3R5cGU7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldHMgdGhlIHBhcmVudCBub2RlXG4gICAgICB1cCgpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNSb290KSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJvb3Qgbm9kZSBoYXMgbm8gcGFyZW50LiBVc2UgZG9jKCkgaWYgeW91IG5lZWQgdG8gZ2V0IHRoZSBkb2N1bWVudCBvYmplY3QuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLnBhcmVudDtcbiAgICAgIH1cblxuICAgICAgLy8gR2V0cyB0aGUgcm9vdCBub2RlXG4gICAgICByb290KCkge1xuICAgICAgICB2YXIgbm9kZTtcbiAgICAgICAgbm9kZSA9IHRoaXM7XG4gICAgICAgIHdoaWxlIChub2RlKSB7XG4gICAgICAgICAgaWYgKG5vZGUudHlwZSA9PT0gTm9kZVR5cGUuRG9jdW1lbnQpIHtcbiAgICAgICAgICAgIHJldHVybiBub2RlLnJvb3RPYmplY3Q7XG4gICAgICAgICAgfSBlbHNlIGlmIChub2RlLmlzUm9vdCkge1xuICAgICAgICAgICAgcmV0dXJuIG5vZGU7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG5vZGUgPSBub2RlLnBhcmVudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gR2V0cyB0aGUgbm9kZSByZXByZXNlbnRpbmcgdGhlIFhNTCBkb2N1bWVudFxuICAgICAgZG9jdW1lbnQoKSB7XG4gICAgICAgIHZhciBub2RlO1xuICAgICAgICBub2RlID0gdGhpcztcbiAgICAgICAgd2hpbGUgKG5vZGUpIHtcbiAgICAgICAgICBpZiAobm9kZS50eXBlID09PSBOb2RlVHlwZS5Eb2N1bWVudCkge1xuICAgICAgICAgICAgcmV0dXJuIG5vZGU7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG5vZGUgPSBub2RlLnBhcmVudDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRW5kcyB0aGUgZG9jdW1lbnQgYW5kIGNvbnZlcnRzIHN0cmluZ1xuICAgICAgZW5kKG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZG9jdW1lbnQoKS5lbmQob3B0aW9ucyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldHMgdGhlIHByZXZpb3VzIG5vZGVcbiAgICAgIHByZXYoKSB7XG4gICAgICAgIHZhciBpO1xuICAgICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgICAgaWYgKGkgPCAxKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQWxyZWFkeSBhdCB0aGUgZmlyc3Qgbm9kZS4gXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQuY2hpbGRyZW5baSAtIDFdO1xuICAgICAgfVxuXG4gICAgICAvLyBHZXRzIHRoZSBuZXh0IG5vZGVcbiAgICAgIG5leHQoKSB7XG4gICAgICAgIHZhciBpO1xuICAgICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgICAgaWYgKGkgPT09IC0xIHx8IGkgPT09IHRoaXMucGFyZW50LmNoaWxkcmVuLmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJBbHJlYWR5IGF0IHRoZSBsYXN0IG5vZGUuIFwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMucGFyZW50LmNoaWxkcmVuW2kgKyAxXTtcbiAgICAgIH1cblxuICAgICAgLy8gSW1wb3J0cyBjbG9uZWQgcm9vdCBmcm9tIGFub3RoZXIgWE1MIGRvY3VtZW50XG5cbiAgICAgIC8vIGBkb2NgIHRoZSBYTUwgZG9jdW1lbnQgdG8gaW5zZXJ0IG5vZGVzIGZyb21cbiAgICAgIGltcG9ydERvY3VtZW50KGRvYykge1xuICAgICAgICB2YXIgY2hpbGQsIGNsb25lZFJvb3QsIGosIGxlbiwgcmVmMTtcbiAgICAgICAgY2xvbmVkUm9vdCA9IGRvYy5yb290KCkuY2xvbmUoKTtcbiAgICAgICAgY2xvbmVkUm9vdC5wYXJlbnQgPSB0aGlzO1xuICAgICAgICBjbG9uZWRSb290LmlzUm9vdCA9IGZhbHNlO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2xvbmVkUm9vdCk7XG4gICAgICAgIC8vIHNldCBwcm9wZXJ0aWVzIGlmIGltcG9ydGVkIGVsZW1lbnQgYmVjb21lcyB0aGUgcm9vdCBub2RlXG4gICAgICAgIGlmICh0aGlzLnR5cGUgPT09IE5vZGVUeXBlLkRvY3VtZW50KSB7XG4gICAgICAgICAgY2xvbmVkUm9vdC5pc1Jvb3QgPSB0cnVlO1xuICAgICAgICAgIGNsb25lZFJvb3QuZG9jdW1lbnRPYmplY3QgPSB0aGlzO1xuICAgICAgICAgIHRoaXMucm9vdE9iamVjdCA9IGNsb25lZFJvb3Q7XG4gICAgICAgICAgLy8gc2V0IGR0ZCBuYW1lXG4gICAgICAgICAgaWYgKHRoaXMuY2hpbGRyZW4pIHtcbiAgICAgICAgICAgIHJlZjEgPSB0aGlzLmNoaWxkcmVuO1xuICAgICAgICAgICAgZm9yIChqID0gMCwgbGVuID0gcmVmMS5sZW5ndGg7IGogPCBsZW47IGorKykge1xuICAgICAgICAgICAgICBjaGlsZCA9IHJlZjFbal07XG4gICAgICAgICAgICAgIGlmIChjaGlsZC50eXBlID09PSBOb2RlVHlwZS5Eb2NUeXBlKSB7XG4gICAgICAgICAgICAgICAgY2hpbGQubmFtZSA9IGNsb25lZFJvb3QubmFtZTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cblxuICAgICAgXG4gICAgICAvLyBSZXR1cm5zIGRlYnVnIHN0cmluZyBmb3IgdGhpcyBub2RlXG4gICAgICBkZWJ1Z0luZm8obmFtZSkge1xuICAgICAgICB2YXIgcmVmMSwgcmVmMjtcbiAgICAgICAgbmFtZSA9IG5hbWUgfHwgdGhpcy5uYW1lO1xuICAgICAgICBpZiAoKG5hbWUgPT0gbnVsbCkgJiYgISgocmVmMSA9IHRoaXMucGFyZW50KSAhPSBudWxsID8gcmVmMS5uYW1lIDogdm9pZCAwKSkge1xuICAgICAgICAgIHJldHVybiBcIlwiO1xuICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT0gbnVsbCkge1xuICAgICAgICAgIHJldHVybiBcInBhcmVudDogPFwiICsgdGhpcy5wYXJlbnQubmFtZSArIFwiPlwiO1xuICAgICAgICB9IGVsc2UgaWYgKCEoKHJlZjIgPSB0aGlzLnBhcmVudCkgIT0gbnVsbCA/IHJlZjIubmFtZSA6IHZvaWQgMCkpIHtcbiAgICAgICAgICByZXR1cm4gXCJub2RlOiA8XCIgKyBuYW1lICsgXCI+XCI7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIFwibm9kZTogPFwiICsgbmFtZSArIFwiPiwgcGFyZW50OiA8XCIgKyB0aGlzLnBhcmVudC5uYW1lICsgXCI+XCI7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQWxpYXNlc1xuICAgICAgZWxlKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICAgIH1cblxuICAgICAgbm9kKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubm9kZShuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICAgIH1cblxuICAgICAgdHh0KHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRleHQodmFsdWUpO1xuICAgICAgfVxuXG4gICAgICBkYXQodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2RhdGEodmFsdWUpO1xuICAgICAgfVxuXG4gICAgICBjb20odmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY29tbWVudCh2YWx1ZSk7XG4gICAgICB9XG5cbiAgICAgIGlucyh0YXJnZXQsIHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmluc3RydWN0aW9uKHRhcmdldCwgdmFsdWUpO1xuICAgICAgfVxuXG4gICAgICBkb2MoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmRvY3VtZW50KCk7XG4gICAgICB9XG5cbiAgICAgIGRlYyh2ZXJzaW9uLCBlbmNvZGluZywgc3RhbmRhbG9uZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5kZWNsYXJhdGlvbih2ZXJzaW9uLCBlbmNvZGluZywgc3RhbmRhbG9uZSk7XG4gICAgICB9XG5cbiAgICAgIGUobmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgICByZXR1cm4gdGhpcy5lbGVtZW50KG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpO1xuICAgICAgfVxuXG4gICAgICBuKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubm9kZShuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICAgIH1cblxuICAgICAgdCh2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy50ZXh0KHZhbHVlKTtcbiAgICAgIH1cblxuICAgICAgZCh2YWx1ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy5jZGF0YSh2YWx1ZSk7XG4gICAgICB9XG5cbiAgICAgIGModmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY29tbWVudCh2YWx1ZSk7XG4gICAgICB9XG5cbiAgICAgIHIodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucmF3KHZhbHVlKTtcbiAgICAgIH1cblxuICAgICAgaSh0YXJnZXQsIHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmluc3RydWN0aW9uKHRhcmdldCwgdmFsdWUpO1xuICAgICAgfVxuXG4gICAgICB1KCkge1xuICAgICAgICByZXR1cm4gdGhpcy51cCgpO1xuICAgICAgfVxuXG4gICAgICAvLyBjYW4gYmUgZGVwcmVjYXRlZCBpbiBhIGZ1dHVyZSByZWxlYXNlXG4gICAgICBpbXBvcnRYTUxCdWlsZGVyKGRvYykge1xuICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnREb2N1bWVudChkb2MpO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGRzIG9yIG1vZGlmaWVzIGFuIGF0dHJpYnV0ZS5cblxuICAgICAgLy8gYG5hbWVgIGF0dHJpYnV0ZSBuYW1lXG4gICAgICAvLyBgdmFsdWVgIGF0dHJpYnV0ZSB2YWx1ZVxuICAgICAgYXR0cmlidXRlKG5hbWUsIHZhbHVlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImF0dHJpYnV0ZSgpIGFwcGxpZXMgdG8gZWxlbWVudCBub2RlcyBvbmx5LlwiKTtcbiAgICAgIH1cblxuICAgICAgYXR0KG5hbWUsIHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmF0dHJpYnV0ZShuYW1lLCB2YWx1ZSk7XG4gICAgICB9XG5cbiAgICAgIGEobmFtZSwgdmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYXR0cmlidXRlKG5hbWUsIHZhbHVlKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmVtb3ZlcyBhbiBhdHRyaWJ1dGVcblxuICAgICAgLy8gYG5hbWVgIGF0dHJpYnV0ZSBuYW1lXG4gICAgICByZW1vdmVBdHRyaWJ1dGUobmFtZSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJhdHRyaWJ1dGUoKSBhcHBsaWVzIHRvIGVsZW1lbnQgbm9kZXMgb25seS5cIik7XG4gICAgICB9XG5cbiAgICAgIC8vIERPTSBsZXZlbCAxIGZ1bmN0aW9ucyB0byBiZSBpbXBsZW1lbnRlZCBsYXRlclxuICAgICAgcmVwbGFjZUNoaWxkKG5ld0NoaWxkLCBvbGRDaGlsZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG5cbiAgICAgIHJlbW92ZUNoaWxkKG9sZENoaWxkKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgYXBwZW5kQ2hpbGQobmV3Q2hpbGQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIiArIHRoaXMuZGVidWdJbmZvKCkpO1xuICAgICAgfVxuXG4gICAgICBoYXNDaGlsZE5vZGVzKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jaGlsZHJlbi5sZW5ndGggIT09IDA7XG4gICAgICB9XG5cbiAgICAgIGNsb25lTm9kZShkZWVwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgbm9ybWFsaXplKCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG5cbiAgICAgIC8vIERPTSBsZXZlbCAyXG4gICAgICBpc1N1cHBvcnRlZChmZWF0dXJlLCB2ZXJzaW9uKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuXG4gICAgICBoYXNBdHRyaWJ1dGVzKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hdHRyaWJzLmxlbmd0aCAhPT0gMDtcbiAgICAgIH1cblxuICAgICAgLy8gRE9NIGxldmVsIDMgZnVuY3Rpb25zIHRvIGJlIGltcGxlbWVudGVkIGxhdGVyXG4gICAgICBjb21wYXJlRG9jdW1lbnRQb3NpdGlvbihvdGhlcikge1xuICAgICAgICB2YXIgcmVmLCByZXM7XG4gICAgICAgIHJlZiA9IHRoaXM7XG4gICAgICAgIGlmIChyZWYgPT09IG90aGVyKSB7XG4gICAgICAgICAgcmV0dXJuIDA7XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5kb2N1bWVudCgpICE9PSBvdGhlci5kb2N1bWVudCgpKSB7XG4gICAgICAgICAgcmVzID0gRG9jdW1lbnRQb3NpdGlvbi5EaXNjb25uZWN0ZWQgfCBEb2N1bWVudFBvc2l0aW9uLkltcGxlbWVudGF0aW9uU3BlY2lmaWM7XG4gICAgICAgICAgaWYgKE1hdGgucmFuZG9tKCkgPCAwLjUpIHtcbiAgICAgICAgICAgIHJlcyB8PSBEb2N1bWVudFBvc2l0aW9uLlByZWNlZGluZztcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmVzIHw9IERvY3VtZW50UG9zaXRpb24uRm9sbG93aW5nO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gcmVzO1xuICAgICAgICB9IGVsc2UgaWYgKHJlZi5pc0FuY2VzdG9yKG90aGVyKSkge1xuICAgICAgICAgIHJldHVybiBEb2N1bWVudFBvc2l0aW9uLkNvbnRhaW5zIHwgRG9jdW1lbnRQb3NpdGlvbi5QcmVjZWRpbmc7XG4gICAgICAgIH0gZWxzZSBpZiAocmVmLmlzRGVzY2VuZGFudChvdGhlcikpIHtcbiAgICAgICAgICByZXR1cm4gRG9jdW1lbnRQb3NpdGlvbi5Db250YWlucyB8IERvY3VtZW50UG9zaXRpb24uRm9sbG93aW5nO1xuICAgICAgICB9IGVsc2UgaWYgKHJlZi5pc1ByZWNlZGluZyhvdGhlcikpIHtcbiAgICAgICAgICByZXR1cm4gRG9jdW1lbnRQb3NpdGlvbi5QcmVjZWRpbmc7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIERvY3VtZW50UG9zaXRpb24uRm9sbG93aW5nO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlzU2FtZU5vZGUob3RoZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIiArIHRoaXMuZGVidWdJbmZvKCkpO1xuICAgICAgfVxuXG4gICAgICBsb29rdXBQcmVmaXgobmFtZXNwYWNlVVJJKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgaXNEZWZhdWx0TmFtZXNwYWNlKG5hbWVzcGFjZVVSSSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG5cbiAgICAgIGxvb2t1cE5hbWVzcGFjZVVSSShwcmVmaXgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIiArIHRoaXMuZGVidWdJbmZvKCkpO1xuICAgICAgfVxuXG4gICAgICBpc0VxdWFsTm9kZShub2RlKSB7XG4gICAgICAgIHZhciBpLCBqLCByZWYxO1xuICAgICAgICBpZiAobm9kZS5ub2RlVHlwZSAhPT0gdGhpcy5ub2RlVHlwZSkge1xuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobm9kZS5jaGlsZHJlbi5sZW5ndGggIT09IHRoaXMuY2hpbGRyZW4ubGVuZ3RoKSB7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGZvciAoaSA9IGogPSAwLCByZWYxID0gdGhpcy5jaGlsZHJlbi5sZW5ndGggLSAxOyAoMCA8PSByZWYxID8gaiA8PSByZWYxIDogaiA+PSByZWYxKTsgaSA9IDAgPD0gcmVmMSA/ICsraiA6IC0taikge1xuICAgICAgICAgIGlmICghdGhpcy5jaGlsZHJlbltpXS5pc0VxdWFsTm9kZShub2RlLmNoaWxkcmVuW2ldKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cblxuICAgICAgZ2V0RmVhdHVyZShmZWF0dXJlLCB2ZXJzaW9uKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgc2V0VXNlckRhdGEoa2V5LCBkYXRhLCBoYW5kbGVyKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgZ2V0VXNlckRhdGEoa2V5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgRE9NIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQuXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmV0dXJucyB0cnVlIGlmIG90aGVyIGlzIGFuIGluY2x1c2l2ZSBkZXNjZW5kYW50IG9mIG5vZGUsXG4gICAgICAvLyBhbmQgZmFsc2Ugb3RoZXJ3aXNlLlxuICAgICAgY29udGFpbnMob3RoZXIpIHtcbiAgICAgICAgaWYgKCFvdGhlcikge1xuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb3RoZXIgPT09IHRoaXMgfHwgdGhpcy5pc0Rlc2NlbmRhbnQob3RoZXIpO1xuICAgICAgfVxuXG4gICAgICAvLyBBbiBvYmplY3QgQSBpcyBjYWxsZWQgYSBkZXNjZW5kYW50IG9mIGFuIG9iamVjdCBCLCBpZiBlaXRoZXIgQSBpcyBcbiAgICAgIC8vIGEgY2hpbGQgb2YgQiBvciBBIGlzIGEgY2hpbGQgb2YgYW4gb2JqZWN0IEMgdGhhdCBpcyBhIGRlc2NlbmRhbnQgb2YgQi5cbiAgICAgIGlzRGVzY2VuZGFudChub2RlKSB7XG4gICAgICAgIHZhciBjaGlsZCwgaXNEZXNjZW5kYW50Q2hpbGQsIGosIGxlbiwgcmVmMTtcbiAgICAgICAgcmVmMSA9IHRoaXMuY2hpbGRyZW47XG4gICAgICAgIGZvciAoaiA9IDAsIGxlbiA9IHJlZjEubGVuZ3RoOyBqIDwgbGVuOyBqKyspIHtcbiAgICAgICAgICBjaGlsZCA9IHJlZjFbal07XG4gICAgICAgICAgaWYgKG5vZGUgPT09IGNoaWxkKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaXNEZXNjZW5kYW50Q2hpbGQgPSBjaGlsZC5pc0Rlc2NlbmRhbnQobm9kZSk7XG4gICAgICAgICAgaWYgKGlzRGVzY2VuZGFudENoaWxkKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBBbiBvYmplY3QgQSBpcyBjYWxsZWQgYW4gYW5jZXN0b3Igb2YgYW4gb2JqZWN0IEIgaWYgYW5kIG9ubHkgaWZcbiAgICAgIC8vIEIgaXMgYSBkZXNjZW5kYW50IG9mIEEuXG4gICAgICBpc0FuY2VzdG9yKG5vZGUpIHtcbiAgICAgICAgcmV0dXJuIG5vZGUuaXNEZXNjZW5kYW50KHRoaXMpO1xuICAgICAgfVxuXG4gICAgICAvLyBBbiBvYmplY3QgQSBpcyBwcmVjZWRpbmcgYW4gb2JqZWN0IEIgaWYgQSBhbmQgQiBhcmUgaW4gdGhlIFxuICAgICAgLy8gc2FtZSB0cmVlIGFuZCBBIGNvbWVzIGJlZm9yZSBCIGluIHRyZWUgb3JkZXIuXG4gICAgICBpc1ByZWNlZGluZyhub2RlKSB7XG4gICAgICAgIHZhciBub2RlUG9zLCB0aGlzUG9zO1xuICAgICAgICBub2RlUG9zID0gdGhpcy50cmVlUG9zaXRpb24obm9kZSk7XG4gICAgICAgIHRoaXNQb3MgPSB0aGlzLnRyZWVQb3NpdGlvbih0aGlzKTtcbiAgICAgICAgaWYgKG5vZGVQb3MgPT09IC0xIHx8IHRoaXNQb3MgPT09IC0xKSB7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBub2RlUG9zIDwgdGhpc1BvcztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBBbiBvYmplY3QgQSBpcyBmb2xsbG93aW5nIGFuIG9iamVjdCBCIGlmIEEgYW5kIEIgYXJlIGluIHRoZSBcbiAgICAgIC8vIHNhbWUgdHJlZSBhbmQgQSBjb21lcyBhZnRlciBCIGluIHRyZWUgb3JkZXIuXG4gICAgICBpc0ZvbGxvd2luZyhub2RlKSB7XG4gICAgICAgIHZhciBub2RlUG9zLCB0aGlzUG9zO1xuICAgICAgICBub2RlUG9zID0gdGhpcy50cmVlUG9zaXRpb24obm9kZSk7XG4gICAgICAgIHRoaXNQb3MgPSB0aGlzLnRyZWVQb3NpdGlvbih0aGlzKTtcbiAgICAgICAgaWYgKG5vZGVQb3MgPT09IC0xIHx8IHRoaXNQb3MgPT09IC0xKSB7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBub2RlUG9zID4gdGhpc1BvcztcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBSZXR1cm5zIHRoZSBwcmVvcmRlciBwb3NpdGlvbiBvZiB0aGUgZ2l2ZW4gbm9kZSBpbiB0aGUgdHJlZSwgb3IgLTFcbiAgICAgIC8vIGlmIHRoZSBub2RlIGlzIG5vdCBpbiB0aGUgdHJlZS5cbiAgICAgIHRyZWVQb3NpdGlvbihub2RlKSB7XG4gICAgICAgIHZhciBmb3VuZCwgcG9zO1xuICAgICAgICBwb3MgPSAwO1xuICAgICAgICBmb3VuZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLmZvcmVhY2hUcmVlTm9kZSh0aGlzLmRvY3VtZW50KCksIGZ1bmN0aW9uKGNoaWxkTm9kZSkge1xuICAgICAgICAgIHBvcysrO1xuICAgICAgICAgIGlmICghZm91bmQgJiYgY2hpbGROb2RlID09PSBub2RlKSB7XG4gICAgICAgICAgICByZXR1cm4gZm91bmQgPSB0cnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChmb3VuZCkge1xuICAgICAgICAgIHJldHVybiBwb3M7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIFxuICAgICAgLy8gRGVwdGgtZmlyc3QgcHJlb3JkZXIgdHJhdmVyc2FsIHRocm91Z2ggdGhlIFhNTCB0cmVlXG4gICAgICBmb3JlYWNoVHJlZU5vZGUobm9kZSwgZnVuYykge1xuICAgICAgICB2YXIgY2hpbGQsIGosIGxlbiwgcmVmMSwgcmVzO1xuICAgICAgICBub2RlIHx8IChub2RlID0gdGhpcy5kb2N1bWVudCgpKTtcbiAgICAgICAgcmVmMSA9IG5vZGUuY2hpbGRyZW47XG4gICAgICAgIGZvciAoaiA9IDAsIGxlbiA9IHJlZjEubGVuZ3RoOyBqIDwgbGVuOyBqKyspIHtcbiAgICAgICAgICBjaGlsZCA9IHJlZjFbal07XG4gICAgICAgICAgaWYgKHJlcyA9IGZ1bmMoY2hpbGQpKSB7XG4gICAgICAgICAgICByZXR1cm4gcmVzO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXMgPSB0aGlzLmZvcmVhY2hUcmVlTm9kZShjaGlsZCwgZnVuYyk7XG4gICAgICAgICAgICBpZiAocmVzKSB7XG4gICAgICAgICAgICAgIHJldHVybiByZXM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICB9O1xuXG4gICAgLy8gRE9NIGxldmVsIDFcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdub2RlTmFtZScsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm5hbWU7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdub2RlVHlwZScsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnR5cGU7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdub2RlVmFsdWUnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy52YWx1ZTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShYTUxOb2RlLnByb3RvdHlwZSwgJ3BhcmVudE5vZGUnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQ7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdjaGlsZE5vZGVzJywge1xuICAgICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgaWYgKCF0aGlzLmNoaWxkTm9kZUxpc3QgfHwgIXRoaXMuY2hpbGROb2RlTGlzdC5ub2Rlcykge1xuICAgICAgICAgIHRoaXMuY2hpbGROb2RlTGlzdCA9IG5ldyBYTUxOb2RlTGlzdCh0aGlzLmNoaWxkcmVuKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5jaGlsZE5vZGVMaXN0O1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KFhNTE5vZGUucHJvdG90eXBlLCAnZmlyc3RDaGlsZCcsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNoaWxkcmVuWzBdIHx8IG51bGw7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdsYXN0Q2hpbGQnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jaGlsZHJlblt0aGlzLmNoaWxkcmVuLmxlbmd0aCAtIDFdIHx8IG51bGw7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdwcmV2aW91c1NpYmxpbmcnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgaTtcbiAgICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICAgIHJldHVybiB0aGlzLnBhcmVudC5jaGlsZHJlbltpIC0gMV0gfHwgbnVsbDtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShYTUxOb2RlLnByb3RvdHlwZSwgJ25leHRTaWJsaW5nJywge1xuICAgICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgdmFyIGk7XG4gICAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQuY2hpbGRyZW5baSArIDFdIHx8IG51bGw7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICdvd25lckRvY3VtZW50Jywge1xuICAgICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZG9jdW1lbnQoKSB8fCBudWxsO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gRE9NIGxldmVsIDNcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MTm9kZS5wcm90b3R5cGUsICd0ZXh0Q29udGVudCcsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBjaGlsZCwgaiwgbGVuLCByZWYxLCBzdHI7XG4gICAgICAgIGlmICh0aGlzLm5vZGVUeXBlID09PSBOb2RlVHlwZS5FbGVtZW50IHx8IHRoaXMubm9kZVR5cGUgPT09IE5vZGVUeXBlLkRvY3VtZW50RnJhZ21lbnQpIHtcbiAgICAgICAgICBzdHIgPSAnJztcbiAgICAgICAgICByZWYxID0gdGhpcy5jaGlsZHJlbjtcbiAgICAgICAgICBmb3IgKGogPSAwLCBsZW4gPSByZWYxLmxlbmd0aDsgaiA8IGxlbjsgaisrKSB7XG4gICAgICAgICAgICBjaGlsZCA9IHJlZjFbal07XG4gICAgICAgICAgICBpZiAoY2hpbGQudGV4dENvbnRlbnQpIHtcbiAgICAgICAgICAgICAgc3RyICs9IGNoaWxkLnRleHRDb250ZW50O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gc3RyO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgc2V0OiBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gWE1MTm9kZTtcblxuICB9KS5jYWxsKHRoaXMpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNodeList.js ***!
  \****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Represents a list of nodes\n  var XMLNodeList;\n\n  module.exports = XMLNodeList = (function() {\n    class XMLNodeList {\n      // Initializes a new instance of `XMLNodeList`\n      // This is just a wrapper around an ordinary\n      // JS array.\n\n      // `nodes` the array containing nodes.\n      constructor(nodes) {\n        this.nodes = nodes;\n      }\n\n      // Creates and returns a deep clone of `this`\n\n      clone() {\n        // this class should not be cloned since it wraps\n        // around a given array. The calling function should check\n        // whether the wrapped array is null and supply a new array\n        // (from the clone).\n        return this.nodes = null;\n      }\n\n      // DOM Level 1\n      item(index) {\n        return this.nodes[index] || null;\n      }\n\n    };\n\n    // DOM level 1\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function() {\n        return this.nodes.length || 0;\n      }\n    });\n\n    return XMLNodeList;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js":
/*!*****************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js ***!
  \*****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLCharacterData, XMLProcessingInstruction;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  // Represents a processing instruction\n  module.exports = XMLProcessingInstruction = class XMLProcessingInstruction extends XMLCharacterData {\n    // Initializes a new instance of `XMLProcessingInstruction`\n\n    // `parent` the parent node\n    // `target` instruction target\n    // `value` instruction value\n    constructor(parent, target, value) {\n      super(parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    // Creates and returns a deep clone of `this`\n    clone() {\n      return Object.create(this);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    }\n\n    isEqualNode(node) {\n      if (!super.isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js":
/*!***********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLRaw.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLNode, XMLRaw;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  // Represents a  raw node\n  module.exports = XMLRaw = class XMLRaw extends XMLNode {\n    // Initializes a new instance of `XMLRaw`\n\n    // `text` raw text\n    constructor(parent, text) {\n      super(parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n\n    // Creates and returns a deep clone of `this`\n    clone() {\n      return Object.create(this);\n    }\n\n    // Converts the XML fragment to string\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation for pretty print\n    // `options.offset` how many indentations to add to every line for pretty print\n    // `options.newline` newline sequence for pretty print\n    toString(options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStreamWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, WriterState, XMLStreamWriter, XMLWriterBase,\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  // Prints XML nodes to a stream\n  module.exports = XMLStreamWriter = class XMLStreamWriter extends XMLWriterBase {\n    // Initializes a new instance of `XMLStreamWriter`\n\n    // `stream` output stream\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation string\n    // `options.newline` newline sequence\n    // `options.offset` a fixed number of indentations to add to every line\n    // `options.allowEmpty` do not self close empty element tags\n    // 'options.dontPrettyTextNodes' if any text is present in node, don't indent or LF\n    // `options.spaceBeforeSlash` add a space before the closing slash of empty elements\n    constructor(stream, options) {\n      super(options);\n      this.stream = stream;\n    }\n\n    endline(node, options, level) {\n      if (node.isLastRootNode && options.state === WriterState.CloseTag) {\n        return '';\n      } else {\n        return super.endline(node, options, level);\n      }\n    }\n\n    document(doc, options) {\n      var child, i, j, k, len1, len2, ref, ref1, results;\n      ref = doc.children;\n      // set a flag so that we don't insert a newline after the last root level node \n      for (i = j = 0, len1 = ref.length; j < len1; i = ++j) {\n        child = ref[i];\n        child.isLastRootNode = i === doc.children.length - 1;\n      }\n      options = this.filterOptions(options);\n      ref1 = doc.children;\n      results = [];\n      for (k = 0, len2 = ref1.length; k < len2; k++) {\n        child = ref1[k];\n        results.push(this.writeChildNode(child, options, 0));\n      }\n      return results;\n    }\n\n    cdata(node, options, level) {\n      return this.stream.write(super.cdata(node, options, level));\n    }\n\n    comment(node, options, level) {\n      return this.stream.write(super.comment(node, options, level));\n    }\n\n    declaration(node, options, level) {\n      return this.stream.write(super.declaration(node, options, level));\n    }\n\n    docType(node, options, level) {\n      var child, j, len1, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      // external identifier\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      // internal subset\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (j = 0, len1 = ref.length; j < len1; j++) {\n          child = ref[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(']');\n      }\n      // close tag\n      options.state = WriterState.CloseTag;\n      this.stream.write(options.spaceBeforeSlash + '>');\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    }\n\n    element(node, options, level) {\n      var att, attLen, child, childNodeCount, firstChildNode, j, len, len1, name, prettySuppressed, r, ratt, ref, ref1, ref2, rline;\n      level || (level = 0);\n      // open tag\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<' + node.name;\n      // attributes\n      if (options.pretty && options.width > 0) {\n        len = r.length;\n        ref = node.attribs;\n        for (name in ref) {\n          if (!hasProp.call(ref, name)) continue;\n          att = ref[name];\n          ratt = this.attribute(att, options, level);\n          attLen = ratt.length;\n          if (len + attLen > options.width) {\n            rline = this.indent(node, options, level + 1) + ratt;\n            r += this.endline(node, options, level) + rline;\n            len = rline.length;\n          } else {\n            rline = ' ' + ratt;\n            r += rline;\n            len += rline.length;\n          }\n        }\n      } else {\n        ref1 = node.attribs;\n        for (name in ref1) {\n          if (!hasProp.call(ref1, name)) continue;\n          att = ref1[name];\n          r += this.attribute(att, options, level);\n        }\n      }\n      this.stream.write(r);\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        // empty element\n        if (options.allowEmpty) {\n          this.stream.write('>');\n          options.state = WriterState.CloseTag;\n          this.stream.write('</' + node.name + '>');\n        } else {\n          options.state = WriterState.CloseTag;\n          this.stream.write(options.spaceBeforeSlash + '/>');\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        // do not indent text-only nodes\n        this.stream.write('>');\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref2 = node.children;\n        // inner tags\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        // close tag\n        options.state = WriterState.CloseTag;\n        this.stream.write(this.indent(node, options, level) + '</' + node.name + '>');\n      }\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    }\n\n    processingInstruction(node, options, level) {\n      return this.stream.write(super.processingInstruction(node, options, level));\n    }\n\n    raw(node, options, level) {\n      return this.stream.write(super.raw(node, options, level));\n    }\n\n    text(node, options, level) {\n      return this.stream.write(super.text(node, options, level));\n    }\n\n    dtdAttList(node, options, level) {\n      return this.stream.write(super.dtdAttList(node, options, level));\n    }\n\n    dtdElement(node, options, level) {\n      return this.stream.write(super.dtdElement(node, options, level));\n    }\n\n    dtdEntity(node, options, level) {\n      return this.stream.write(super.dtdEntity(node, options, level));\n    }\n\n    dtdNotation(node, options, level) {\n      return this.stream.write(super.dtdNotation(node, options, level));\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var XMLStringWriter, XMLWriterBase;\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  // Prints XML nodes as plain text\n  module.exports = XMLStringWriter = class XMLStringWriter extends XMLWriterBase {\n    // Initializes a new instance of `XMLStringWriter`\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation string\n    // `options.newline` newline sequence\n    // `options.offset` a fixed number of indentations to add to every line\n    // `options.allowEmpty` do not self close empty element tags\n    // 'options.dontPrettyTextNodes' if any text is present in node, don't indent or LF\n    // `options.spaceBeforeSlash` add a space before the closing slash of empty elements\n    constructor(options) {\n      super(options);\n    }\n\n    document(doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      // remove trailing newline\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    }\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MU3RyaW5nV3JpdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsbUJBQU8sQ0FBQyw2RUFBaUI7O0FBRTNDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFNBQVM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxub2RlX21vZHVsZXNcXHhtbGJ1aWxkZXJcXGxpYlxcWE1MU3RyaW5nV3JpdGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMi40LjFcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTFN0cmluZ1dyaXRlciwgWE1MV3JpdGVyQmFzZTtcblxuICBYTUxXcml0ZXJCYXNlID0gcmVxdWlyZSgnLi9YTUxXcml0ZXJCYXNlJyk7XG5cbiAgLy8gUHJpbnRzIFhNTCBub2RlcyBhcyBwbGFpbiB0ZXh0XG4gIG1vZHVsZS5leHBvcnRzID0gWE1MU3RyaW5nV3JpdGVyID0gY2xhc3MgWE1MU3RyaW5nV3JpdGVyIGV4dGVuZHMgWE1MV3JpdGVyQmFzZSB7XG4gICAgLy8gSW5pdGlhbGl6ZXMgYSBuZXcgaW5zdGFuY2Ugb2YgYFhNTFN0cmluZ1dyaXRlcmBcblxuICAgIC8vIGBvcHRpb25zLnByZXR0eWAgcHJldHR5IHByaW50cyB0aGUgcmVzdWx0XG4gICAgLy8gYG9wdGlvbnMuaW5kZW50YCBpbmRlbnRhdGlvbiBzdHJpbmdcbiAgICAvLyBgb3B0aW9ucy5uZXdsaW5lYCBuZXdsaW5lIHNlcXVlbmNlXG4gICAgLy8gYG9wdGlvbnMub2Zmc2V0YCBhIGZpeGVkIG51bWJlciBvZiBpbmRlbnRhdGlvbnMgdG8gYWRkIHRvIGV2ZXJ5IGxpbmVcbiAgICAvLyBgb3B0aW9ucy5hbGxvd0VtcHR5YCBkbyBub3Qgc2VsZiBjbG9zZSBlbXB0eSBlbGVtZW50IHRhZ3NcbiAgICAvLyAnb3B0aW9ucy5kb250UHJldHR5VGV4dE5vZGVzJyBpZiBhbnkgdGV4dCBpcyBwcmVzZW50IGluIG5vZGUsIGRvbid0IGluZGVudCBvciBMRlxuICAgIC8vIGBvcHRpb25zLnNwYWNlQmVmb3JlU2xhc2hgIGFkZCBhIHNwYWNlIGJlZm9yZSB0aGUgY2xvc2luZyBzbGFzaCBvZiBlbXB0eSBlbGVtZW50c1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIH1cblxuICAgIGRvY3VtZW50KGRvYywgb3B0aW9ucykge1xuICAgICAgdmFyIGNoaWxkLCBpLCBsZW4sIHIsIHJlZjtcbiAgICAgIG9wdGlvbnMgPSB0aGlzLmZpbHRlck9wdGlvbnMob3B0aW9ucyk7XG4gICAgICByID0gJyc7XG4gICAgICByZWYgPSBkb2MuY2hpbGRyZW47XG4gICAgICBmb3IgKGkgPSAwLCBsZW4gPSByZWYubGVuZ3RoOyBpIDwgbGVuOyBpKyspIHtcbiAgICAgICAgY2hpbGQgPSByZWZbaV07XG4gICAgICAgIHIgKz0gdGhpcy53cml0ZUNoaWxkTm9kZShjaGlsZCwgb3B0aW9ucywgMCk7XG4gICAgICB9XG4gICAgICAvLyByZW1vdmUgdHJhaWxpbmcgbmV3bGluZVxuICAgICAgaWYgKG9wdGlvbnMucHJldHR5ICYmIHIuc2xpY2UoLW9wdGlvbnMubmV3bGluZS5sZW5ndGgpID09PSBvcHRpb25zLm5ld2xpbmUpIHtcbiAgICAgICAgciA9IHIuc2xpY2UoMCwgLW9wdGlvbnMubmV3bGluZS5sZW5ndGgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHI7XG4gICAgfVxuXG4gIH07XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringifier.js ***!
  \*******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  // Converts values to strings\n  var XMLStringifier,\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    class XMLStringifier {\n      // Initializes a new instance of `XMLStringifier`\n\n      // `options.version` The version number string of the XML spec to validate against, e.g. 1.0\n      // `options.noDoubleEncoding` whether existing html entities are encoded: true or false\n      // `options.stringify` a set of functions to use for converting values to strings\n      // `options.noValidation` whether values will be validated and escaped or returned as is\n      constructor(options) {\n        var key, ref, value;\n        // Checks whether the given string contains legal characters\n        // Fails with an exception on error\n\n        // `str` the string to check\n        this.assertLegalChar = this.assertLegalChar.bind(this);\n        // Checks whether the given string contains legal characters for a name\n        // Fails with an exception on error\n\n        // `str` the string to check\n        this.assertLegalName = this.assertLegalName.bind(this);\n        options || (options = {});\n        this.options = options;\n        if (!this.options.version) {\n          this.options.version = '1.0';\n        }\n        ref = options.stringify || {};\n        for (key in ref) {\n          if (!hasProp.call(ref, key)) continue;\n          value = ref[key];\n          this[key] = value;\n        }\n      }\n\n      // Defaults\n      name(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalName('' + val || '');\n      }\n\n      text(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar(this.textEscape('' + val || ''));\n      }\n\n      cdata(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        val = '' + val || '';\n        val = val.replace(']]>', ']]]]><![CDATA[>');\n        return this.assertLegalChar(val);\n      }\n\n      comment(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        val = '' + val || '';\n        if (val.match(/--/)) {\n          throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n        }\n        return this.assertLegalChar(val);\n      }\n\n      raw(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return '' + val || '';\n      }\n\n      attValue(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar(this.attEscape(val = '' + val || ''));\n      }\n\n      insTarget(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      insValue(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        val = '' + val || '';\n        if (val.match(/\\?>/)) {\n          throw new Error(\"Invalid processing instruction value: \" + val);\n        }\n        return this.assertLegalChar(val);\n      }\n\n      xmlVersion(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        val = '' + val || '';\n        if (!val.match(/1\\.[0-9]+/)) {\n          throw new Error(\"Invalid version number: \" + val);\n        }\n        return val;\n      }\n\n      xmlEncoding(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        val = '' + val || '';\n        if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n          throw new Error(\"Invalid encoding: \" + val);\n        }\n        return this.assertLegalChar(val);\n      }\n\n      xmlStandalone(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        if (val) {\n          return \"yes\";\n        } else {\n          return \"no\";\n        }\n      }\n\n      dtdPubID(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdSysID(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdElementValue(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdAttType(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdAttDefault(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdEntityValue(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      dtdNData(val) {\n        if (this.options.noValidation) {\n          return val;\n        }\n        return this.assertLegalChar('' + val || '');\n      }\n\n      assertLegalChar(str) {\n        var regex, res;\n        if (this.options.noValidation) {\n          return str;\n        }\n        regex = '';\n        if (this.options.version === '1.0') {\n          // Valid characters from https://www.w3.org/TR/xml/#charsets\n          // any Unicode character, excluding the surrogate blocks, FFFE, and FFFF.\n          // #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]\n          // This ES5 compatible Regexp has been generated using the \"regenerate\" NPM module:\n          //   let xml_10_InvalidChars = regenerate()\n          //     .addRange(0x0000, 0x0008)\n          //     .add(0x000B, 0x000C)\n          //     .addRange(0x000E, 0x001F)\n          //     .addRange(0xD800, 0xDFFF)\n          //     .addRange(0xFFFE, 0xFFFF)\n          regex = /[\\0-\\x08\\x0B\\f\\x0E-\\x1F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n          if (res = str.match(regex)) {\n            throw new Error(`Invalid character in string: ${str} at index ${res.index}`);\n          }\n        } else if (this.options.version === '1.1') {\n          // Valid characters from https://www.w3.org/TR/xml11/#charsets\n          // any Unicode character, excluding the surrogate blocks, FFFE, and FFFF.\n          // [#x1-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]\n          // This ES5 compatible Regexp has been generated using the \"regenerate\" NPM module:\n          //   let xml_11_InvalidChars = regenerate()\n          //     .add(0x0000)\n          //     .addRange(0xD800, 0xDFFF)\n          //     .addRange(0xFFFE, 0xFFFF)\n          regex = /[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n          if (res = str.match(regex)) {\n            throw new Error(`Invalid character in string: ${str} at index ${res.index}`);\n          }\n        }\n        return str;\n      }\n\n      assertLegalName(str) {\n        var regex;\n        if (this.options.noValidation) {\n          return str;\n        }\n        this.assertLegalChar(str);\n        regex = /^([:A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])([\\x2D\\.0-:A-Z_a-z\\xB7\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u203F\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])*$/;\n        if (!str.match(regex)) {\n          throw new Error(\"Invalid character in name\");\n        }\n        return str;\n      }\n\n      // Escapes special characters in text\n\n      // See http://www.w3.org/TR/2000/WD-xml-c14n-20000119.html#charescaping\n\n      // `str` the string to escape\n      textEscape(str) {\n        var ampregex;\n        if (this.options.noValidation) {\n          return str;\n        }\n        ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n        return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n      }\n\n      // Escapes special characters in attribute values\n\n      // See http://www.w3.org/TR/2000/WD-xml-c14n-20000119.html#charescaping\n\n      // `str` the string to escape\n      attEscape(str) {\n        var ampregex;\n        if (this.options.noValidation) {\n          return str;\n        }\n        ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n        return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n      }\n\n    };\n\n    // strings to match while converting from JS objects\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    return XMLStringifier;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLText.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLText.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, XMLCharacterData, XMLText;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  // Represents a text node\n  module.exports = XMLText = (function() {\n    class XMLText extends XMLCharacterData {\n      // Initializes a new instance of `XMLText`\n\n      // `text` element text\n      constructor(parent, text) {\n        super(parent);\n        if (text == null) {\n          throw new Error(\"Missing element text. \" + this.debugInfo());\n        }\n        this.name = \"#text\";\n        this.type = NodeType.Text;\n        this.value = this.stringify.text(text);\n      }\n\n      // Creates and returns a deep clone of `this`\n      clone() {\n        return Object.create(this);\n      }\n\n      // Converts the XML fragment to string\n\n      // `options.pretty` pretty prints the result\n      // `options.indent` indentation for pretty print\n      // `options.offset` how many indentations to add to every line for pretty print\n      // `options.newline` newline sequence for pretty print\n      toString(options) {\n        return this.options.writer.text(this, this.options.writer.filterOptions(options));\n      }\n\n      // DOM level 1 functions to be implemented later\n      splitText(offset) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n      // DOM level 3 functions to be implemented later\n      replaceWholeText(content) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n\n    };\n\n    // DOM level 3\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function() {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n\n    return XMLText;\n\n  }).call(this);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLWriterBase.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, WriterState, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLText, XMLWriterBase, assign,\n    hasProp = {}.hasOwnProperty;\n\n  ({assign} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  // Base class for XML writers\n  module.exports = XMLWriterBase = class XMLWriterBase {\n    // Initializes a new instance of `XMLWriterBase`\n\n    // `options.pretty` pretty prints the result\n    // `options.indent` indentation string\n    // `options.newline` newline sequence\n    // `options.offset` a fixed number of indentations to add to every line\n    // `options.width` maximum column width\n    // `options.allowEmpty` do not self close empty element tags\n    // 'options.dontPrettyTextNodes' if any text is present in node, don't indent or LF\n    // `options.spaceBeforeSlash` add a space before the closing slash of empty elements\n    constructor(options) {\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[\"_\" + key] = this[key];\n        this[key] = value;\n      }\n    }\n\n    // Filters writer options and provides defaults\n\n    // `options` writer options\n    filterOptions(options) {\n      var filteredOptions, ref, ref1, ref2, ref3, ref4, ref5, ref6, ref7;\n      options || (options = {});\n      options = assign({}, this.options, options);\n      filteredOptions = {\n        writer: this\n      };\n      filteredOptions.pretty = options.pretty || false;\n      filteredOptions.allowEmpty = options.allowEmpty || false;\n      filteredOptions.indent = (ref = options.indent) != null ? ref : '  ';\n      filteredOptions.newline = (ref1 = options.newline) != null ? ref1 : '\\n';\n      filteredOptions.offset = (ref2 = options.offset) != null ? ref2 : 0;\n      filteredOptions.width = (ref3 = options.width) != null ? ref3 : 0;\n      filteredOptions.dontPrettyTextNodes = (ref4 = (ref5 = options.dontPrettyTextNodes) != null ? ref5 : options.dontprettytextnodes) != null ? ref4 : 0;\n      filteredOptions.spaceBeforeSlash = (ref6 = (ref7 = options.spaceBeforeSlash) != null ? ref7 : options.spacebeforeslash) != null ? ref6 : '';\n      if (filteredOptions.spaceBeforeSlash === true) {\n        filteredOptions.spaceBeforeSlash = ' ';\n      }\n      filteredOptions.suppressPrettyCount = 0;\n      filteredOptions.user = {};\n      filteredOptions.state = WriterState.None;\n      return filteredOptions;\n    }\n\n    // Returns the indentation string for the current level\n\n    // `node` current node\n    // `options` writer options\n    // `level` current indentation level\n    indent(node, options, level) {\n      var indentLevel;\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else if (options.pretty) {\n        indentLevel = (level || 0) + options.offset + 1;\n        if (indentLevel > 0) {\n          return new Array(indentLevel).join(options.indent);\n        }\n      }\n      return '';\n    }\n\n    // Returns the newline string\n\n    // `node` current node\n    // `options` writer options\n    // `level` current indentation level\n    endline(node, options, level) {\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else {\n        return options.newline;\n      }\n    }\n\n    attribute(att, options, level) {\n      var r;\n      this.openAttribute(att, options, level);\n      if (options.pretty && options.width > 0) {\n        r = att.name + '=\"' + att.value + '\"';\n      } else {\n        r = ' ' + att.name + '=\"' + att.value + '\"';\n      }\n      this.closeAttribute(att, options, level);\n      return r;\n    }\n\n    cdata(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<![CDATA[';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ']]>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    comment(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!-- ';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ' -->' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    declaration(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?xml';\n      options.state = WriterState.InsideTag;\n      r += ' version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    docType(node, options, level) {\n      var child, i, len1, r, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      r += '<!DOCTYPE ' + node.root().name;\n      // external identifier\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      // internal subset\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (i = 0, len1 = ref.length; i < len1; i++) {\n          child = ref[i];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += ']';\n      }\n      // close tag\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    element(node, options, level) {\n      var att, attLen, child, childNodeCount, firstChildNode, i, j, len, len1, len2, name, prettySuppressed, r, ratt, ref, ref1, ref2, ref3, rline;\n      level || (level = 0);\n      prettySuppressed = false;\n      // open tag\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<' + node.name;\n      // attributes\n      if (options.pretty && options.width > 0) {\n        len = r.length;\n        ref = node.attribs;\n        for (name in ref) {\n          if (!hasProp.call(ref, name)) continue;\n          att = ref[name];\n          ratt = this.attribute(att, options, level);\n          attLen = ratt.length;\n          if (len + attLen > options.width) {\n            rline = this.indent(node, options, level + 1) + ratt;\n            r += this.endline(node, options, level) + rline;\n            len = rline.length;\n          } else {\n            rline = ' ' + ratt;\n            r += rline;\n            len += rline.length;\n          }\n        }\n      } else {\n        ref1 = node.attribs;\n        for (name in ref1) {\n          if (!hasProp.call(ref1, name)) continue;\n          att = ref1[name];\n          r += this.attribute(att, options, level);\n        }\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        // empty element\n        if (options.allowEmpty) {\n          r += '>';\n          options.state = WriterState.CloseTag;\n          r += '</' + node.name + '>' + this.endline(node, options, level);\n        } else {\n          options.state = WriterState.CloseTag;\n          r += options.spaceBeforeSlash + '/>' + this.endline(node, options, level);\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        // do not indent text-only nodes\n        r += '>';\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        r += this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        r += '</' + node.name + '>' + this.endline(node, options, level);\n      } else {\n        // if ANY are a text node, then suppress pretty now\n        if (options.dontPrettyTextNodes) {\n          ref2 = node.children;\n          for (i = 0, len1 = ref2.length; i < len1; i++) {\n            child = ref2[i];\n            if ((child.type === NodeType.Text || child.type === NodeType.Raw) && (child.value != null)) {\n              options.suppressPrettyCount++;\n              prettySuppressed = true;\n              break;\n            }\n          }\n        }\n        // close the opening tag, after dealing with newline\n        r += '>' + this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref3 = node.children;\n        // inner tags\n        for (j = 0, len2 = ref3.length; j < len2; j++) {\n          child = ref3[j];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        // close tag\n        options.state = WriterState.CloseTag;\n        r += this.indent(node, options, level) + '</' + node.name + '>';\n        if (prettySuppressed) {\n          options.suppressPrettyCount--;\n        }\n        r += this.endline(node, options, level);\n        options.state = WriterState.None;\n      }\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    writeChildNode(node, options, level) {\n      switch (node.type) {\n        case NodeType.CData:\n          return this.cdata(node, options, level);\n        case NodeType.Comment:\n          return this.comment(node, options, level);\n        case NodeType.Element:\n          return this.element(node, options, level);\n        case NodeType.Raw:\n          return this.raw(node, options, level);\n        case NodeType.Text:\n          return this.text(node, options, level);\n        case NodeType.ProcessingInstruction:\n          return this.processingInstruction(node, options, level);\n        case NodeType.Dummy:\n          return '';\n        case NodeType.Declaration:\n          return this.declaration(node, options, level);\n        case NodeType.DocType:\n          return this.docType(node, options, level);\n        case NodeType.AttributeDeclaration:\n          return this.dtdAttList(node, options, level);\n        case NodeType.ElementDeclaration:\n          return this.dtdElement(node, options, level);\n        case NodeType.EntityDeclaration:\n          return this.dtdEntity(node, options, level);\n        case NodeType.NotationDeclaration:\n          return this.dtdNotation(node, options, level);\n        default:\n          throw new Error(\"Unknown XML node type: \" + node.constructor.name);\n      }\n    }\n\n    processingInstruction(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?';\n      options.state = WriterState.InsideTag;\n      r += node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    raw(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    text(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    dtdAttList(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ATTLIST';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    dtdElement(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ELEMENT';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name + ' ' + node.value;\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    dtdEntity(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ENTITY';\n      options.state = WriterState.InsideTag;\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    dtdNotation(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!NOTATION';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    }\n\n    openNode(node, options, level) {}\n\n    closeNode(node, options, level) {}\n\n    openAttribute(att, options, level) {}\n\n    closeAttribute(att, options, level) {}\n\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/index.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 2.4.1\n(function() {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction;\n\n  ({assign, isFunction} = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"));\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLDocumentCB = __webpack_require__(/*! ./XMLDocumentCB */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  XMLStreamWriter = __webpack_require__(/*! ./XMLStreamWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  // Creates a new document and returns the root node for\n  // chain-building the document tree\n\n  // `name` name of the root element\n\n  // `xmldec.version` A version number string, e.g. 1.0\n  // `xmldec.encoding` Encoding declaration, e.g. UTF-8\n  // `xmldec.standalone` standalone document declaration: true or false\n\n  // `doctype.pubID` public identifier of the external subset\n  // `doctype.sysID` system identifier of the external subset\n\n  // `options.headless` whether XML declaration and doctype will be included:\n  //     true or false\n  // `options.keepNullNodes` whether nodes with null values will be kept\n  //     or ignored: true or false\n  // `options.keepNullAttributes` whether attributes with null values will be\n  //     kept or ignored: true or false\n  // `options.ignoreDecorators` whether decorator strings will be ignored when\n  //     converting JS objects: true or false\n  // `options.separateArrayItems` whether array items are created as separate\n  //     nodes when passed as an object value: true or false\n  // `options.noDoubleEncoding` whether existing html entities are encoded:\n  //     true or false\n  // `options.stringify` a set of functions to use for converting values to\n  //     strings\n  // `options.writer` the default XML writer to use for converting nodes to\n  //     string. If the default writer is not set, the built-in XMLStringWriter\n  //     will be used instead.\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    // create the document node\n    doc = new XMLDocument(options);\n    // add the root node\n    root = doc.element(name);\n    // prolog\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n\n  // Creates a new document and returns the document node for\n  // chain-building the document tree\n\n  // `options.keepNullNodes` whether nodes with null values will be kept\n  //     or ignored: true or false\n  // `options.keepNullAttributes` whether attributes with null values will be\n  //     kept or ignored: true or false\n  // `options.ignoreDecorators` whether decorator strings will be ignored when\n  //     converting JS objects: true or false\n  // `options.separateArrayItems` whether array items are created as separate\n  //     nodes when passed as an object value: true or false\n  // `options.noDoubleEncoding` whether existing html entities are encoded:\n  //     true or false\n  // `options.stringify` a set of functions to use for converting values to\n  //     strings\n  // `options.writer` the default XML writer to use for converting nodes to\n  //     string. If the default writer is not set, the built-in XMLStringWriter\n  //     will be used instead.\n\n  // `onData` the function to be called when a new chunk of XML is output. The\n  //          string containing the XML chunk is passed to `onData` as its single\n  //          argument.\n  // `onEnd`  the function to be called when the XML document is completed with\n  //          `end`. `onEnd` does not receive any arguments.\n  module.exports.begin = function(options, onData, onEnd) {\n    if (isFunction(options)) {\n      [onData, onEnd] = [options, onData];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n  module.exports.implementation = new XMLDOMImplementation();\n\n  module.exports.nodeType = NodeType;\n\n  module.exports.writerState = WriterState;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/index.js\n");

/***/ })

};
;