import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { AtualizarAgendamentoData, StatusAgendamento } from '@/types/agendamentos';

// GET - Buscar agendamento específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const resolvedParams = await params;
    const agendamentoId = parseInt(resolvedParams.id);

    if (isNaN(agendamentoId)) {
      return NextResponse.json(
        { success: false, error: 'ID do agendamento inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar agendamento com dados relacionados
    let query = supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        colaborador_user_id,
        servico_id,
        data_hora_inicio,
        data_hora_fim,
        observacoes_cliente,
        status_agendamento,
        forma_pagamento,
        status_pagamento,
        valor_total,
        valor_desconto,
        codigo_confirmacao,
        prazo_confirmacao,
        created_at,
        updated_at,
        empresas!inner (
          nome_empresa,
          endereco,
          telefone
        ),
        servicos!inner (
          nome_servico,
          descricao,
          duracao_minutos,
          preco,
          categoria
        )
      `)
      .eq('agendamento_id', agendamentoId);

    // Aplicar filtros baseados no papel do usuário
    const userRole = user.user_metadata?.role;
    
    if (userRole === 'Usuario') {
      // Cliente só vê seus próprios agendamentos
      query = query.eq('cliente_user_id', user.id);
    } else if (userRole === 'Colaborador') {
      // Colaborador vê agendamentos onde ele é o prestador
      query = query.eq('colaborador_user_id', user.id);
    } else if (userRole === 'Proprietario') {
      // Proprietário vê agendamentos da sua empresa
      // Usar cliente admin para evitar problemas de RLS
      const supabaseAdmin = createAdminClient();
      const { data: empresa } = await supabaseAdmin
        .from('empresas')
        .select('empresa_id')
        .eq('proprietario_user_id', user.id)
        .eq('status', 'ativo')
        .single();

      if (empresa) {
        query = query.eq('empresa_id', empresa.empresa_id);
      } else {
        return NextResponse.json(
          { success: false, error: 'Empresa não encontrada' },
          { status: 404 }
        );
      }
    } else if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    const { data: agendamento, error: agendamentoError } = await query.single();

    if (agendamentoError) {
      console.error('Erro ao buscar agendamento:', agendamentoError);
      return NextResponse.json(
        { success: false, error: 'Agendamento não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agendamento
    });

  } catch (error: any) {
    console.error('Erro geral ao buscar agendamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PATCH - Atualizar agendamento (confirmar/recusar/cancelar)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const resolvedParams = await params;
    const agendamentoId = parseInt(resolvedParams.id);

    if (isNaN(agendamentoId)) {
      return NextResponse.json(
        { success: false, error: 'ID do agendamento inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body: AtualizarAgendamentoData = await request.json();
    const { status_agendamento, status_pagamento, observacoes_cliente } = body;

    // Buscar agendamento atual
    const { data: agendamentoAtual, error: buscarError } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        colaborador_user_id,
        status_agendamento,
        forma_pagamento,
        status_pagamento,
        valor_total,
        prazo_confirmacao,
        empresas!inner (
          proprietario_user_id
        )
      `)
      .eq('agendamento_id', agendamentoId)
      .single();

    if (buscarError || !agendamentoAtual) {
      return NextResponse.json(
        { success: false, error: 'Agendamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar permissões
    const userRole = user.user_metadata?.role;
    const empresa = Array.isArray(agendamentoAtual.empresas) ? agendamentoAtual.empresas[0] : agendamentoAtual.empresas;
    const podeAtualizar =
      userRole === 'Administrador' ||
      (userRole === 'Proprietario' && empresa?.proprietario_user_id === user.id) ||
      (userRole === 'Colaborador' && agendamentoAtual.colaborador_user_id === user.id) ||
      (userRole === 'Usuario' && agendamentoAtual.cliente_user_id === user.id);

    if (!podeAtualizar) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para atualizar este agendamento' },
        { status: 403 }
      );
    }

    // Validar transições de status
    const statusAtual = agendamentoAtual.status_agendamento;
    
    if (status_agendamento) {
      // Validar se a transição é permitida
      const transicoesPermitidas: { [key: string]: StatusAgendamento[] } = {
        'Pendente': ['Confirmado', 'Recusado', 'Cancelado'],
        'Confirmado': ['Cancelado', 'Concluido'],
        'Recusado': [], // Status final
        'Cancelado': [], // Status final
        'Concluido': [] // Status final
      };

      if (!transicoesPermitidas[statusAtual]?.includes(status_agendamento)) {
        return NextResponse.json(
          { success: false, error: `Não é possível alterar status de ${statusAtual} para ${status_agendamento}` },
          { status: 400 }
        );
      }

      // Verificar prazo de confirmação para agendamentos pendentes
      if (statusAtual === 'Pendente' && status_agendamento === 'Confirmado') {
        const prazoConfirmacao = new Date(agendamentoAtual.prazo_confirmacao);
        const agora = new Date();
        
        if (agora > prazoConfirmacao) {
          return NextResponse.json(
            { success: false, error: 'Prazo de confirmação expirado' },
            { status: 400 }
          );
        }
      }
    }

    // Preparar dados para atualização
    const dadosAtualizacao: any = {
      updated_at: new Date().toISOString()
    };

    if (status_agendamento) {
      dadosAtualizacao.status_agendamento = status_agendamento;
    }

    if (status_pagamento) {
      dadosAtualizacao.status_pagamento = status_pagamento;
    }

    if (observacoes_cliente !== undefined) {
      dadosAtualizacao.observacoes_cliente = observacoes_cliente;
    }

    // Atualizar agendamento
    const { data: agendamentoAtualizado, error: atualizarError } = await supabase
      .from('agendamentos')
      .update(dadosAtualizacao)
      .eq('agendamento_id', agendamentoId)
      .select()
      .single();

    if (atualizarError) {
      console.error('Erro ao atualizar agendamento:', atualizarError);
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar agendamento' },
        { status: 500 }
      );
    }

    console.log('✅ Agendamento atualizado com sucesso:', agendamentoAtualizado);

    // Enviar notificações por email baseadas na mudança de status (não bloquear a resposta)
    if (status_agendamento && status_agendamento !== statusAtual) {
      try {
        const {
          notificarConfirmacaoAgendamento,
          notificarRecusaAgendamento,
          notificarCancelamentoAgendamento
        } = await import('@/utils/notificationHelpers');

        // Executar em background sem aguardar
        if (status_agendamento === 'Confirmado') {
          notificarConfirmacaoAgendamento(agendamentoId).catch(error => {
            console.error('❌ Erro ao enviar notificação de confirmação:', error);
          });
        } else if (status_agendamento === 'Recusado') {
          notificarRecusaAgendamento(agendamentoId).catch(error => {
            console.error('❌ Erro ao enviar notificação de recusa:', error);
          });
        } else if (status_agendamento === 'Cancelado') {
          notificarCancelamentoAgendamento(agendamentoId).catch(error => {
            console.error('❌ Erro ao enviar notificação de cancelamento:', error);
          });
        }
      } catch (error) {
        console.error('❌ Erro ao importar helpers de notificação:', error);
      }
    }

    // TODO: Implementar lógica de reembolso automático para cancelamentos

    return NextResponse.json({
      success: true,
      data: agendamentoAtualizado,
      message: 'Agendamento atualizado com sucesso'
    });

  } catch (error: any) {
    console.error('Erro geral ao atualizar agendamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
