import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { CriarAgendamentoData, StatusAgendamento, AtualizarAgendamentoData } from '@/types/agendamentos';

// GET - Buscar agendamentos
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Extrair filtros da query
    const empresa_id = searchParams.get('empresa_id');
    const status_agendamento = searchParams.get('status_agendamento');
    const data_inicio = searchParams.get('data_inicio');
    const data_fim = searchParams.get('data_fim');

    // Construir query base
    let query = supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        colaborador_user_id,
        servico_id,
        data_hora_inicio,
        data_hora_fim,
        observacoes_cliente,
        status_agendamento,
        forma_pagamento,
        status_pagamento,
        valor_total,
        valor_desconto,
        codigo_confirmacao,
        prazo_confirmacao,
        created_at,
        updated_at,
        empresas!inner (
          nome_empresa,
          endereco,
          telefone
        ),
        servicos!inner (
          nome_servico,
          descricao,
          duracao_minutos,
          preco,
          categoria
        ),
        colaboradores:auth.users!colaborador_user_id (
          id,
          user_metadata
        ),
        clientes:auth.users!cliente_user_id (
          id,
          user_metadata
        )
      `);

    // Aplicar filtros baseados no papel do usuário
    const userRole = user.user_metadata?.role;
    
    if (userRole === 'Usuario') {
      // Cliente só vê seus próprios agendamentos
      query = query.eq('cliente_user_id', user.id);
    } else if (userRole === 'Colaborador') {
      // Colaborador vê agendamentos onde ele é o prestador
      query = query.eq('colaborador_user_id', user.id);
    } else if (userRole === 'Proprietario') {
      // Proprietário vê agendamentos da sua empresa
      if (!empresa_id) {
        // Buscar empresa do proprietário usando cliente admin para evitar problemas de RLS
        const supabaseAdmin = createAdminClient();
        const { data: empresa } = await supabaseAdmin
          .from('empresas')
          .select('empresa_id')
          .eq('proprietario_user_id', user.id)
          .eq('status', 'ativo')
          .single();

        if (empresa) {
          query = query.eq('empresa_id', empresa.empresa_id);
        } else {
          return NextResponse.json(
            { success: false, error: 'Empresa não encontrada' },
            { status: 404 }
          );
        }
      } else {
        query = query.eq('empresa_id', empresa_id);
      }
    } else if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Aplicar filtros adicionais
    if (empresa_id && userRole === 'Administrador') {
      query = query.eq('empresa_id', empresa_id);
    }
    if (status_agendamento) {
      query = query.eq('status_agendamento', status_agendamento);
    }
    if (data_inicio) {
      query = query.gte('data_hora_inicio', data_inicio);
    }
    if (data_fim) {
      query = query.lte('data_hora_inicio', data_fim + ' 23:59:59');
    }

    // Ordenar por data
    query = query.order('data_hora_inicio', { ascending: true });

    const { data: agendamentos, error: agendamentosError } = await query;

    if (agendamentosError) {
      console.error('Erro ao buscar agendamentos:', agendamentosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar agendamentos' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agendamentos || []
    });

  } catch (error: any) {
    console.error('Erro geral na API de agendamentos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Criar novo agendamento
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body: CriarAgendamentoData = await request.json();
    const {
      empresa_id,
      colaborador_user_id,
      servicos_ids,
      data_hora_inicio,
      observacoes_cliente,
      forma_pagamento,
      combo_id,
      valor_desconto
    } = body;

    // Validar dados obrigatórios
    if (!empresa_id || !servicos_ids || servicos_ids.length === 0 || !data_hora_inicio || !forma_pagamento) {
      return NextResponse.json(
        { success: false, error: 'Dados obrigatórios: empresa_id, servicos_ids (array), data_hora_inicio, forma_pagamento' },
        { status: 400 }
      );
    }

    // Verificar se a empresa existe e está ativa
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa, status')
      .eq('empresa_id', empresa_id)
      .eq('status', 'ativo')
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada ou inativa' },
        { status: 404 }
      );
    }

    // Verificar se os serviços existem e estão ativos
    const { data: servicos, error: servicosError } = await supabase
      .from('servicos')
      .select('servico_id, nome_servico, duracao_minutos, preco, ativo')
      .in('servico_id', servicos_ids)
      .eq('empresa_id', empresa_id)
      .eq('ativo', true);

    if (servicosError || !servicos || servicos.length !== servicos_ids.length) {
      return NextResponse.json(
        { success: false, error: 'Um ou mais serviços não foram encontrados ou estão inativos' },
        { status: 404 }
      );
    }

    // Calcular duração total e valor total
    const duracaoTotalMinutos = servicos.reduce((total, servico) => total + servico.duracao_minutos, 0);
    const valorOriginal = servicos.reduce((total, servico) => total + servico.preco, 0);
    const valorFinal = valorOriginal - (valor_desconto || 0);

    // Calcular data/hora de fim baseado na duração total dos serviços
    const dataHoraInicio = new Date(data_hora_inicio);
    const dataHoraFim = new Date(dataHoraInicio.getTime() + duracaoTotalMinutos * 60000);

    // Se colaborador não foi especificado, usar round-robin
    let colaboradorSelecionado = colaborador_user_id;

    if (!colaboradorSelecionado) {
      const { data: colaboradoresDisponiveis } = await supabase
        .from('colaboradores_empresa')
        .select(`
          colaborador_user_id,
          auth_users:colaborador_user_id (
            raw_user_meta_data
          ),
          colaborador_servicos!inner (
            servico_id,
            ativo
          )
        `)
        .eq('empresa_id', empresa_id)
        .eq('ativo', true)
        .eq('ativo_como_prestador', true)
        .in('colaborador_servicos.servico_id', servicos_ids)
        .eq('colaborador_servicos.ativo', true);

      if (colaboradoresDisponiveis && colaboradoresDisponiveis.length > 0) {
        // Implementação de round-robin inteligente
        try {
          const { selecionarColaboradorRoundRobin } = await import('@/utils/roundRobin');

          const colaboradoresParaRoundRobin = colaboradoresDisponiveis.map(c => {
            const authUser = Array.isArray(c.auth_users) ? c.auth_users[0] : c.auth_users;
            return {
              colaborador_user_id: c.colaborador_user_id,
              name: authUser?.raw_user_meta_data?.name || 'Colaborador',
              email: authUser?.raw_user_meta_data?.email || '',
              total_agendamentos: 0 // Será calculado pela função
            };
          });

          const resultadoRoundRobin = await selecionarColaboradorRoundRobin({
            empresa_id,
            servico_id: servicos_ids[0], // Usar primeiro serviço para round-robin
            colaboradores_disponiveis: colaboradoresParaRoundRobin,
            data_hora_inicio: dataHoraInicio.toISOString(),
            data_hora_fim: dataHoraFim.toISOString()
          });

          colaboradorSelecionado = resultadoRoundRobin.colaborador_selecionado;

          console.log('✅ Round-Robin aplicado:', {
            colaborador_selecionado: colaboradorSelecionado,
            motivo: resultadoRoundRobin.motivo_selecao,
            total_colaboradores: colaboradoresDisponiveis.length
          });

        } catch (error) {
          console.error('❌ Erro no round-robin, usando fallback aleatório:', error);
          // Fallback para seleção aleatória em caso de erro
          const indiceAleatorio = Math.floor(Math.random() * colaboradoresDisponiveis.length);
          colaboradorSelecionado = colaboradoresDisponiveis[indiceAleatorio].colaborador_user_id;
        }
      } else {
        return NextResponse.json(
          { success: false, error: 'Nenhum colaborador disponível para este serviço' },
          { status: 400 }
        );
      }
    }

    // Verificar se o colaborador pode realizar todos os serviços
    const { data: colaboradorServicos } = await supabase
      .from('colaborador_servicos')
      .select('col_serv_id, servico_id')
      .eq('colaborador_user_id', colaboradorSelecionado)
      .in('servico_id', servicos_ids)
      .eq('ativo', true);

    if (!colaboradorServicos || colaboradorServicos.length !== servicos_ids.length) {
      return NextResponse.json(
        { success: false, error: 'Colaborador não pode realizar todos os serviços selecionados' },
        { status: 400 }
      );
    }

    // Verificar conflitos de horário
    const { data: conflitos } = await supabase
      .from('agendamentos')
      .select('agendamento_id')
      .eq('colaborador_user_id', colaboradorSelecionado)
      .in('status_agendamento', ['Pendente', 'Confirmado'])
      .or(`and(data_hora_inicio.lt.${dataHoraFim.toISOString()},data_hora_fim.gt.${dataHoraInicio.toISOString()})`);

    if (conflitos && conflitos.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Horário não disponível - conflito com outro agendamento' },
        { status: 409 }
      );
    }

    // Calcular prazo de confirmação (24 horas)
    const prazoConfirmacao = new Date(Date.now() + 24 * 60 * 60 * 1000);

    // Criar agendamento principal (usando o primeiro serviço como referência)
    const { data: novoAgendamento, error: criarError } = await supabase
      .from('agendamentos')
      .insert([{
        cliente_user_id: user.id,
        empresa_id: empresa_id,
        colaborador_user_id: colaboradorSelecionado,
        servico_id: servicos_ids[0], // Primeiro serviço como principal
        data_hora_inicio: dataHoraInicio.toISOString(),
        data_hora_fim: dataHoraFim.toISOString(),
        observacoes_cliente: observacoes_cliente || '',
        status_agendamento: 'Pendente' as StatusAgendamento,
        forma_pagamento: forma_pagamento,
        status_pagamento: forma_pagamento === 'Online' ? 'Pendente' : 'Pendente',
        valor_total: valorFinal,
        valor_desconto: valor_desconto || 0,
        prazo_confirmacao: prazoConfirmacao.toISOString()
      }])
      .select()
      .single();

    if (criarError) {
      console.error('Erro ao criar agendamento:', criarError);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar agendamento' },
        { status: 500 }
      );
    }

    // Criar registros para serviços adicionais (se houver mais de um)
    if (servicos_ids.length > 1) {
      const servicosAdicionais = servicos_ids.slice(1).map((servico_id, index) => ({
        agendamento_id: novoAgendamento.agendamento_id,
        servico_id: servico_id,
        ordem_execucao: index + 2 // Começar do 2, pois o primeiro já está no agendamento principal
      }));

      const { error: servicosError } = await supabase
        .from('agendamento_servicos')
        .insert(servicosAdicionais);

      if (servicosError) {
        console.error('Erro ao criar serviços adicionais:', servicosError);
        // Não falhar o agendamento por isso, apenas logar
      }
    }

    // Registrar combo aplicado (se houver)
    if (combo_id && valor_desconto && valor_desconto > 0) {
      const { error: comboError } = await supabase
        .from('agendamento_combos')
        .insert([{
          agendamento_id: novoAgendamento.agendamento_id,
          combo_id: combo_id,
          valor_desconto: valor_desconto,
          aplicado_em: new Date().toISOString()
        }]);

      if (comboError) {
        console.error('Erro ao registrar combo aplicado:', comboError);
        // Não falhar o agendamento por isso, apenas logar
      }
    }

    console.log('✅ Agendamento criado com sucesso:', {
      agendamento_id: novoAgendamento.agendamento_id,
      servicos_count: servicos_ids.length,
      valor_original: valorOriginal,
      valor_desconto: valor_desconto || 0,
      valor_final: valorFinal,
      combo_aplicado: !!combo_id
    });

    // Enviar notificações por email (não bloquear a resposta)
    try {
      const { notificarNovoAgendamento } = await import('@/utils/notificationHelpers');
      // Executar em background sem aguardar
      notificarNovoAgendamento(novoAgendamento.agendamento_id).catch(error => {
        console.error('❌ Erro ao enviar notificações de novo agendamento:', error);
      });
    } catch (error) {
      console.error('❌ Erro ao importar helper de notificações:', error);
    }

    return NextResponse.json({
      success: true,
      data: novoAgendamento,
      message: 'Agendamento criado com sucesso'
    });

  } catch (error: any) {
    console.error('Erro geral ao criar agendamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
