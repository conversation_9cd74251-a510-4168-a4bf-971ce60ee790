import { NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar empresa do proprietário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id, nome_empresa, status')
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError) {
      console.error('Erro ao buscar empresa do proprietário:', empresaError);
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    if (!empresa) {
      return NextResponse.json(
        { success: false, error: 'Nenhuma empresa encontrada para este proprietário' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      empresa
    });

  } catch (error: unknown) {
    console.error('Erro geral na API de empresa do proprietário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
