/**
 * API para Relatórios Financeiros (Premium)
 * Implementação da Tarefa #20 - Desenvolver <PERSON><PERSON><PERSON><PERSON> de Relatórios
 * 
 * GET /api/relatorios/financeiro
 * Retorna relatórios financeiros detalhados para plano Premium
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { RelatorioFinanceiro, PeriodoRelatorio } from '@/types/relatorios';
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, 
         startOfQuarter, endOfQuarter, startOfYear, endOfYear, parseISO, format,
         subMonths, subYears } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Buscar empresa do usuário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        planos_saas!inner(nome_plano),
        modelo_negocio
      `)
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se é plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json(
        { success: false, error: 'Recurso disponível apenas no Plano Premium' },
        { status: 403 }
      );
    }

    // Extrair parâmetros da query
    const searchParams = request.nextUrl.searchParams;
    const periodo = (searchParams.get('periodo') || 'mes') as PeriodoRelatorio;
    const dataInicio = searchParams.get('data_inicio');
    const dataFim = searchParams.get('data_fim');

    // Calcular datas do período
    const agora = new Date();
    let inicio: Date, fim: Date;

    switch (periodo) {
      case 'dia':
        inicio = startOfDay(agora);
        fim = endOfDay(agora);
        break;
      case 'semana':
        inicio = startOfWeek(agora, { locale: ptBR });
        fim = endOfWeek(agora, { locale: ptBR });
        break;
      case 'mes':
        inicio = startOfMonth(agora);
        fim = endOfMonth(agora);
        break;
      case 'trimestre':
        inicio = startOfQuarter(agora);
        fim = endOfQuarter(agora);
        break;
      case 'ano':
        inicio = startOfYear(agora);
        fim = endOfYear(agora);
        break;
      case 'personalizado':
        if (!dataInicio || !dataFim) {
          return NextResponse.json(
            { success: false, error: 'Período personalizado requer data_inicio e data_fim' },
            { status: 400 }
          );
        }
        inicio = parseISO(dataInicio);
        fim = parseISO(dataFim);
        break;
      default:
        inicio = startOfMonth(agora);
        fim = endOfMonth(agora);
    }

    // Buscar agendamentos do período
    const { data: agendamentos, error: agendamentosError } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        data_hora_inicio,
        status_agendamento,
        forma_pagamento,
        status_pagamento,
        valor_total,
        valor_desconto,
        colaborador_user_id,
        colaboradores_empresa!inner(
          percentual_comissao,
          custos_operacionais
        )
      `)
      .eq('empresa_id', empresa.empresa_id)
      .gte('data_hora_inicio', inicio.toISOString())
      .lte('data_hora_inicio', fim.toISOString())
      .in('status_agendamento', ['Confirmado', 'Concluido']);

    if (agendamentosError) {
      console.error('Erro ao buscar agendamentos:', agendamentosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar dados financeiros' },
        { status: 500 }
      );
    }

    // Calcular resumo financeiro
    const receitaTotal = agendamentos.reduce((total, a) => total + (parseFloat(a.valor_total) || 0), 0);
    const receitaOnline = agendamentos
      .filter(a => a.forma_pagamento === 'Online')
      .reduce((total, a) => total + (parseFloat(a.valor_total) || 0), 0);
    const receitaLocal = agendamentos
      .filter(a => a.forma_pagamento === 'Local')
      .reduce((total, a) => total + (parseFloat(a.valor_total) || 0), 0);

    // Calcular comissões e custos
    let comissoesPagas = 0;
    let custosOperacionais = 0;

    agendamentos.forEach(agendamento => {
      const valor = parseFloat(agendamento.valor_total) || 0;
      const colaboradorData = Array.isArray(agendamento.colaboradores_empresa) 
        ? agendamento.colaboradores_empresa[0] 
        : agendamento.colaboradores_empresa;
      
      if (colaboradorData) {
        const comissao = valor * (colaboradorData.percentual_comissao || 0) / 100;
        comissoesPagas += comissao;
        custosOperacionais += colaboradorData.custos_operacionais || 0;
      }
    });

    const lucroLiquido = receitaTotal - comissoesPagas - custosOperacionais;

    // Evolução mensal (últimos 12 meses)
    const evolucaoMensal = [];
    for (let i = 11; i >= 0; i--) {
      const mesInicio = startOfMonth(subMonths(agora, i));
      const mesFim = endOfMonth(subMonths(agora, i));
      
      const { data: agendamentosMes } = await supabase
        .from('agendamentos')
        .select(`
          valor_total,
          colaboradores_empresa!inner(
            percentual_comissao,
            custos_operacionais
          )
        `)
        .eq('empresa_id', empresa.empresa_id)
        .gte('data_hora_inicio', mesInicio.toISOString())
        .lte('data_hora_inicio', mesFim.toISOString())
        .in('status_agendamento', ['Confirmado', 'Concluido']);

      const receitaMes = agendamentosMes?.reduce((total, a) => total + (parseFloat(a.valor_total) || 0), 0) || 0;
      
      let custosMes = 0;
      agendamentosMes?.forEach(a => {
        const valor = parseFloat(a.valor_total) || 0;
        const colaboradorData = Array.isArray(a.colaboradores_empresa) 
          ? a.colaboradores_empresa[0] 
          : a.colaboradores_empresa;
        
        if (colaboradorData) {
          const comissao = valor * (colaboradorData.percentual_comissao || 0) / 100;
          custosMes += comissao + (colaboradorData.custos_operacionais || 0);
        }
      });

      evolucaoMensal.push({
        mes: format(mesInicio, 'MMM/yyyy', { locale: ptBR }),
        receita: receitaMes,
        custos: custosMes,
        lucro: receitaMes - custosMes
      });
    }

    // Comparativo com período anterior
    let inicioAnterior: Date, fimAnterior: Date;
    
    switch (periodo) {
      case 'dia':
        inicioAnterior = startOfDay(subMonths(agora, 0));
        fimAnterior = endOfDay(subMonths(agora, 0));
        break;
      case 'semana':
        inicioAnterior = startOfWeek(subMonths(agora, 0), { locale: ptBR });
        fimAnterior = endOfWeek(subMonths(agora, 0), { locale: ptBR });
        break;
      case 'mes':
        inicioAnterior = startOfMonth(subMonths(agora, 1));
        fimAnterior = endOfMonth(subMonths(agora, 1));
        break;
      case 'trimestre':
        inicioAnterior = startOfQuarter(subMonths(agora, 3));
        fimAnterior = endOfQuarter(subMonths(agora, 3));
        break;
      case 'ano':
        inicioAnterior = startOfYear(subYears(agora, 1));
        fimAnterior = endOfYear(subYears(agora, 1));
        break;
      default:
        inicioAnterior = startOfMonth(subMonths(agora, 1));
        fimAnterior = endOfMonth(subMonths(agora, 1));
    }

    // Buscar dados do período anterior para comparação
    const { data: agendamentosAnteriores } = await supabase
      .from('agendamentos')
      .select('agendamento_id, valor_total')
      .eq('empresa_id', empresa.empresa_id)
      .gte('data_hora_inicio', inicioAnterior.toISOString())
      .lte('data_hora_inicio', fimAnterior.toISOString())
      .in('status_agendamento', ['Confirmado', 'Concluido']);

    const receitaAnterior = agendamentosAnteriores?.reduce((total, a) => total + (parseFloat(a.valor_total) || 0), 0) || 0;
    const agendamentosAnterior = agendamentosAnteriores?.length || 0;
    const ticketMedioAnterior = agendamentosAnterior > 0 ? receitaAnterior / agendamentosAnterior : 0;
    const ticketMedioAtual = agendamentos.length > 0 ? receitaTotal / agendamentos.length : 0;

    const relatorioFinanceiro: RelatorioFinanceiro = {
      periodo: {
        inicio: inicio.toISOString(),
        fim: fim.toISOString()
      },
      resumo_financeiro: {
        receita_total: receitaTotal,
        receita_online: receitaOnline,
        receita_local: receitaLocal,
        comissoes_pagas: comissoesPagas,
        custos_operacionais: custosOperacionais,
        lucro_liquido: lucroLiquido
      },
      evolucao_mensal: evolucaoMensal,
      comparativo_periodo_anterior: {
        receita_variacao: receitaAnterior > 0 ? ((receitaTotal - receitaAnterior) / receitaAnterior) * 100 : 0,
        agendamentos_variacao: agendamentosAnterior > 0 ? ((agendamentos.length - agendamentosAnterior) / agendamentosAnterior) * 100 : 0,
        ticket_medio_variacao: ticketMedioAnterior > 0 ? ((ticketMedioAtual - ticketMedioAnterior) / ticketMedioAnterior) * 100 : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: relatorioFinanceiro,
      plano_empresa: 'premium',
      gerado_em: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro na API de relatórios financeiros:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
